"""
Fact Builder for IDR XML Mapping Generator
Based on IDR User Guide 2024Q4 SP1 lines 4317-4340
"""

from typing import List, Dict, Optional, Set
import logging
from core.schema_parser import SchemaAnalysis, XSDElement, XSDComplexType, TypeMapper
from core.idr_types import (
    IDRFact, IDRVariable, IDRDimension, IDRNamingConventions,
    GenerationConfig
)

logger = logging.getLogger(__name__)


class FactBuilder:
    """
    Builds IDR Fact definitions from XSD schema analysis
    Based on User Guide examples from lines 4317-4340
    """
    
    def __init__(self, config: GenerationConfig):
        self.config = config
        self.type_mapper = TypeMapper()
        self.naming = IDRNamingConventions()
    
    def build_facts(self, schema_analysis: SchemaAnalysis, 
                   dimensions: List[IDRDimension]) -> List[IDRFact]:
        """Build facts from schema analysis"""
        facts = []
        
        if not self.config.auto_create_facts:
            return facts
        
        # Create facts from complex types
        for complex_type in schema_analysis.complex_types:
            fact = self._create_fact_from_complex_type(complex_type, dimensions)
            if fact:
                facts.append(fact)
        
        # Create facts from root elements
        for element in schema_analysis.root_elements:
            if self._should_create_fact_for_element(element):
                fact = self._create_fact_from_element(element, dimensions, schema_analysis)
                if fact:
                    facts.append(fact)
        
        logger.info(f"Created {len(facts)} facts from schema")
        return facts
    
    def _create_fact_from_complex_type(self, complex_type: XSDComplexType, 
                                     dimensions: List[IDRDimension]) -> Optional[IDRFact]:
        """Create fact from XSD complex type"""
        try:
            # Generate names
            fact_name = self.naming.generate_fact_name(complex_type.name)
            table_name = self.naming.generate_table_name(complex_type.name, is_dimension=False)
            
            # Create variables
            variables = self._create_standard_variables()
            
            # Add dimension foreign keys
            dimension_variables = self._create_dimension_variables(dimensions)
            variables.extend(dimension_variables)
            
            # Add data variables from complex type elements
            data_variables = self._create_data_variables(complex_type.elements)
            variables.extend(data_variables)
            
            # Add variables from attributes
            attribute_variables = self._create_data_variables(complex_type.attributes)
            variables.extend(attribute_variables)
            
            return IDRFact(
                name=fact_name,
                table_name=table_name,
                connection="T1",  # Default target connection
                variables=variables,
                merge_enabled=self.config.enable_merge_operations
            )
            
        except Exception as e:
            logger.error(f"Error creating fact from complex type {complex_type.name}: {e}")
            return None
    
    def _create_fact_from_element(self, element: XSDElement, dimensions: List[IDRDimension],
                                schema_analysis: SchemaAnalysis) -> Optional[IDRFact]:
        """Create fact from root element"""
        try:
            # Generate names
            fact_name = self.naming.generate_fact_name(element.name)
            table_name = self.naming.generate_table_name(element.name, is_dimension=False)
            
            # Create variables
            variables = self._create_standard_variables()
            
            # Add dimension foreign keys
            dimension_variables = self._create_dimension_variables(dimensions)
            variables.extend(dimension_variables)
            
            # Add variable for the element itself
            element_variable = self._create_variable_from_element(element)
            if element_variable:
                variables.append(element_variable)
            
            return IDRFact(
                name=fact_name,
                table_name=table_name,
                connection="T1",
                variables=variables,
                merge_enabled=self.config.enable_merge_operations
            )
            
        except Exception as e:
            logger.error(f"Error creating fact from element {element.name}: {e}")
            return None
    
    def _create_standard_variables(self) -> List[IDRVariable]:
        """Create standard IDR variables that appear in all facts"""
        # Based on User Guide examples from lines 4323-4327
        standard_variables = [
            IDRVariable(
                name="JobId",
                type="int",
                description="IDR job identifier"
            ),
            IDRVariable(
                name="RunNumber",
                type="int",
                description="Prophet run number"
            ),
            IDRVariable(
                name="SPCode",
                type="int",
                description="SP Code from Prophet"
            ),
            IDRVariable(
                name="MonthId",
                type="int",
                description="Time period identifier"
            )
        ]
        
        return standard_variables
    
    def _create_dimension_variables(self, dimensions: List[IDRDimension]) -> List[IDRVariable]:
        """Create foreign key variables for dimensions"""
        dimension_variables = []
        
        for dimension in dimensions:
            # Create foreign key variable
            variable = IDRVariable(
                name=dimension.id_column,
                type="int",
                description=f"Foreign key to {dimension.name}"
            )
            dimension_variables.append(variable)
        
        return dimension_variables
    
    def _create_data_variables(self, elements: List[XSDElement]) -> List[IDRVariable]:
        """Create data variables from XSD elements"""
        variables = []
        
        for element in elements:
            variable = self._create_variable_from_element(element)
            if variable:
                variables.append(variable)
        
        return variables
    
    def _create_variable_from_element(self, element: XSDElement) -> Optional[IDRVariable]:
        """Create variable from XSD element"""
        try:
            # Map type
            idr_type = self.type_mapper.map_xsd_to_idr(element.type)
            
            # Sanitize name
            variable_name = self.naming.sanitize_name(element.name)
            
            # Determine if this should be a match column for merge operations
            is_match = self._should_be_match_column(element)
            
            return IDRVariable(
                name=variable_name,
                type=idr_type,
                is_match=is_match,
                description=element.documentation
            )
            
        except Exception as e:
            logger.warning(f"Error creating variable from element {element.name}: {e}")
            return None
    
    def _should_create_fact_for_element(self, element: XSDElement) -> bool:
        """Determine if element should become a fact"""
        # Create facts for elements that look like data/measurements
        fact_indicators = ['amount', 'value', 'total', 'count', 'rate', 'premium', 'benefit']
        element_name_lower = element.name.lower()
        
        # Also create facts for numeric elements
        numeric_types = ['decimal', 'double', 'float', 'int', 'integer']
        is_numeric = any(num_type in element.type.lower() for num_type in numeric_types)
        
        has_fact_indicator = any(indicator in element_name_lower for indicator in fact_indicators)
        
        return has_fact_indicator or is_numeric
    
    def _should_be_match_column(self, element: XSDElement) -> bool:
        """Determine if element should be a match column for merge operations"""
        if not self.config.enable_merge_operations:
            return False
        
        # Key fields that should be used for matching
        match_indicators = ['id', 'key', 'number', 'code']
        element_name_lower = element.name.lower()
        
        return any(indicator in element_name_lower for indicator in match_indicators)
    
    def create_prophet_fact(self) -> IDRFact:
        """Create a standard Prophet results fact table"""
        # Based on common Prophet variables
        variables = self._create_standard_variables()
        
        # Add common Prophet variables
        prophet_variables = [
            IDRVariable("ProductId", "int", description="Product dimension foreign key"),
            IDRVariable("DEATH_OUTGO", "double", description="Death benefits outgo"),
            IDRVariable("SURR_OUTGO", "double", description="Surrender benefits outgo"),
            IDRVariable("ANNUAL_PREM", "double", description="Annual premium"),
            IDRVariable("RESERVES", "double", description="Mathematical reserves"),
            IDRVariable("CASH_FLOW", "double", description="Net cash flow"),
            IDRVariable("PRESENT_VALUE", "double", description="Present value"),
            IDRVariable("NEW_BUSINESS", "double", description="New business indicator"),
            IDRVariable("POLICY_COUNT", "int", description="Number of policies")
        ]
        
        variables.extend(prophet_variables)
        
        return IDRFact(
            name="FactProphetResults",
            table_name="FactProphetResults",
            connection="T1",
            variables=variables,
            merge_enabled=self.config.enable_merge_operations
        )
    
    def optimize_fact_structure(self, facts: List[IDRFact]) -> List[IDRFact]:
        """Optimize fact structure by removing duplicates and consolidating"""
        optimized_facts = []
        seen_tables = set()
        
        for fact in facts:
            # Skip duplicate table names
            if fact.table_name in seen_tables:
                logger.info(f"Skipping duplicate fact table: {fact.table_name}")
                continue
            
            seen_tables.add(fact.table_name)
            
            # Remove duplicate variables within fact
            unique_variables = []
            seen_variable_names = set()
            
            for variable in fact.variables:
                if variable.name not in seen_variable_names:
                    unique_variables.append(variable)
                    seen_variable_names.add(variable.name)
            
            fact.variables = unique_variables
            optimized_facts.append(fact)
        
        return optimized_facts
    
    def validate_facts(self, facts: List[IDRFact]) -> List[Dict[str, str]]:
        """Validate fact definitions"""
        validation_errors = []
        
        fact_names = set()
        table_names = set()
        
        for fact in facts:
            # Check for duplicate names
            if fact.name in fact_names:
                validation_errors.append({
                    'type': 'duplicate_name',
                    'fact': fact.name,
                    'message': f'Duplicate fact name: {fact.name}'
                })
            fact_names.add(fact.name)
            
            # Check for duplicate table names
            if fact.table_name in table_names:
                validation_errors.append({
                    'type': 'duplicate_table',
                    'fact': fact.name,
                    'message': f'Duplicate table name: {fact.table_name}'
                })
            table_names.add(fact.table_name)
            
            # Validate variables
            variable_names = set()
            for variable in fact.variables:
                # Check for duplicate variable names
                if variable.name in variable_names:
                    validation_errors.append({
                        'type': 'duplicate_variable',
                        'fact': fact.name,
                        'variable': variable.name,
                        'message': f'Duplicate variable name: {variable.name}'
                    })
                variable_names.add(variable.name)
                
                # Validate variable type
                if variable.type not in ['string', 'int', 'double', 'bool', 'long']:
                    validation_errors.append({
                        'type': 'invalid_variable_type',
                        'fact': fact.name,
                        'variable': variable.name,
                        'message': f'Invalid variable type: {variable.type}'
                    })
            
            # Check for required standard variables
            required_variables = {'JobId', 'RunNumber', 'SPCode', 'MonthId'}
            fact_variable_names = {var.name for var in fact.variables}
            missing_variables = required_variables - fact_variable_names
            
            for missing_var in missing_variables:
                validation_errors.append({
                    'type': 'missing_required_variable',
                    'fact': fact.name,
                    'variable': missing_var,
                    'message': f'Missing required variable: {missing_var}'
                })
        
        return validation_errors
