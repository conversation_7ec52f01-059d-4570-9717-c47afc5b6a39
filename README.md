# IDR XML Mapping Generator

A Python tool for generating IDR (Insurance Data Repository) XML mappings from XSD schemas, specifically designed for IDR Stream mode based on the IDR User Guide 2024Q4 SP1.

## Features

- **XSD Schema Parsing**: Comprehensive parsing of XSD files with type mapping to IDR types
- **Automatic Dimension Generation**: Creates IDR dimensions from XSD complex types and elements
- **Fact Table Generation**: Builds IDR fact tables with proper variable typing
- **Pipeline Generation**: Creates IDR Stream mode pipelines with assignments and targets
- **XML Generation**: Produces complete IDR TransformDefinition XML
- **Validation**: Built-in validation for generated XML structures
- **Flexible Configuration**: Configurable generation options and naming conventions

## Based on IDR User Guide 2024Q4 SP1

This tool implements the IDR Stream mode specifications from the official IDR User Guide:

- **TransformDefinition Structure** (lines 4201-4220)
- **Dimension Definitions** (lines 4246-4270) 
- **Fact Definitions** (lines 4317-4340)
- **Pipeline Structure** (lines 4739-4750)
- **Stream Variables** (lines 4857-4890)
- **Assignment Types** (lines 4956-4970)

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd IDRXMLMappingGenerator

# Install dependencies
pip install -r requirements.txt
```

## Quick Start

### Basic Usage

```bash
# Generate IDR XML from XSD file
python src/main.py input.xsd -o output.xml -n "MyTransform"

# Generate with validation
python src/main.py input.xsd -o output.xml --validate

# Generate components separately
python src/main.py input.xsd --components -n "MyTransform"
```

### Python API

```python
from src.main import IDRXMLMappingGenerator
from src.core.idr_types import GenerationConfig, CoreStreamType

# Create configuration
config = GenerationConfig(
    auto_create_dimensions=True,
    auto_create_facts=True,
    enable_merge_operations=True,
    default_core_stream=CoreStreamType.PRJM,
    enable_validation=True
)

# Create generator
generator = IDRXMLMappingGenerator(config)

# Generate IDR XML
xml_output = generator.generate_from_xsd("input.xsd", "MyTransform")

# Save to file
with open("output.xml", "w") as f:
    f.write(xml_output)
```

## Architecture

### Core Components

1. **XSDParser** (`src/core/schema_parser.py`)
   - Parses XSD files and extracts structure
   - Maps XSD types to IDR types
   - Analyzes constraints and restrictions

2. **DimensionBuilder** (`src/generators/dimension_builder.py`)
   - Creates IDR dimensions from XSD complex types
   - Generates standard dimensions (Time, Run, SPCode)
   - Handles autoload and dynamic attributes

3. **FactBuilder** (`src/generators/fact_builder.py`)
   - Builds IDR fact tables from schema elements
   - Creates standard variables (JobId, RunNumber, etc.)
   - Supports merge operations

4. **PipelineGenerator** (`src/generators/pipeline_generator.py`)
   - Generates IDR Stream mode pipelines
   - Creates assignments and stream variables
   - Supports multi-stage pipelines

5. **XMLGenerator** (`src/generators/xml_generator.py`)
   - Produces final IDR XML output
   - Formats XML with proper indentation
   - Validates XML structure

### Data Types

The `src/core/idr_types.py` module defines all IDR-specific data structures:

- `TransformDefinition`: Complete IDR transform
- `IDRDimension`: Dimension table definition
- `IDRFact`: Fact table definition  
- `IDRPipeline`: Pipeline with stages
- `StreamVariable`: Stream mode variables
- `Assignment`: Variable assignments

## Configuration Options

### GenerationConfig

```python
config = GenerationConfig(
    auto_create_dimensions=True,      # Auto-generate dimensions
    auto_create_facts=True,           # Auto-generate facts
    enable_merge_operations=False,    # Enable merge in facts
    default_core_stream=CoreStreamType.IMP,  # Default stream type
    enable_validation=True,           # Enable validation pipeline
    naming_convention="PascalCase",   # Naming convention
    include_documentation=True        # Include XML comments
)
```

### Core Stream Types

- `PRJM`: Monthly projections
- `PRJY`: Yearly projections
- `STOM`: Monthly stochastic
- `STOY`: Yearly stochastic
- `IMP`: Individual model points
- `IDRM`: IDR monthly (for IDR-to-IDR)

## Examples

### Generated Dimension

```xml
<Dimension autoload="true" dynamic="false">
  <Name>DimProduct</Name>
  <TableName>DimProduct</TableName>
  <Connection>T1</Connection>
  <Key type="string">ProductName</Key>
  <Id>ProductId</Id>
  <UsableColumns>
    <UsableColumn type="string">Region</UsableColumn>
    <UsableColumn type="double">Factor</UsableColumn>
  </UsableColumns>
</Dimension>
```

### Generated Fact

```xml
<Fact merge="true">
  <Name>FactResults</Name>
  <TableName>FactResults</TableName>
  <Connection>T1</Connection>
  <Variables>
    <Variable type="int">JobId</Variable>
    <Variable type="int">ProductId</Variable>
    <Variable type="int">RunNumber</Variable>
    <Variable type="double">PremiumAmount</Variable>
  </Variables>
</Fact>
```

### Generated Pipeline

```xml
<Pipeline validate="true">
  <Name>MainDataPipeline</Name>
  <InitialStage>
    <Name>InitialStage</Name>
    <CoreStream>IMP</CoreStream>
    <Filters>
      <RunNumbers>
        <Value>1</Value>
      </RunNumbers>
    </Filters>
    <StreamVariables>
      <StreamVariable type="double" autoassign="true">
        <VariableName>PremiumAmount</VariableName>
        <SourceVariable>ANNUAL_PREM</SourceVariable>
      </StreamVariable>
    </StreamVariables>
    <Assignments>
      <Assignment type="int">
        <VariableName>JobId</VariableName>
        <Key>JobId</Key>
      </Assignment>
    </Assignments>
    <Targets>
      <Target>
        <Fact>FactResults</Fact>
      </Target>
    </Targets>
  </InitialStage>
</Pipeline>
```

## Command Line Options

```bash
python src/main.py [OPTIONS] XSD_FILE

Options:
  -o, --output PATH          Output file path (default: stdout)
  -n, --name TEXT           Transform definition name
  --log-level LEVEL         Logging level (DEBUG|INFO|WARNING|ERROR)
  --components              Generate components separately
  --validate                Validate generated XML
  --core-stream TYPE        Default core stream type
  --enable-merge            Enable merge operations in facts
  --help                    Show help message
```

## Validation

The tool includes comprehensive validation:

- **Schema Validation**: Validates XSD parsing results
- **Dimension Validation**: Checks for duplicate names, valid types
- **Fact Validation**: Ensures required variables, valid types
- **XML Validation**: Validates generated XML structure
- **IDR Compliance**: Ensures compliance with IDR User Guide specifications

## Logging

Comprehensive logging is provided:

```python
# Enable debug logging
python src/main.py input.xsd --log-level DEBUG

# Log file is automatically created: idr_generator.log
```

## Testing

```bash
# Run tests
pytest tests/

# Run with coverage
pytest --cov=src tests/

# Run specific test
pytest tests/test_dimension_builder.py
```

## Contributing

1. Follow the IDR User Guide 2024Q4 SP1 specifications
2. Add tests for new features
3. Update documentation
4. Use black for code formatting
5. Run flake8 for linting

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:

1. Check the IDR User Guide 2024Q4 SP1 documentation
2. Review the generated XML against IDR specifications
3. Enable debug logging for troubleshooting
4. Validate generated XML using the built-in validator

## Roadmap

- [ ] Support for additional XSD features
- [ ] Enhanced validation rules
- [ ] GUI interface
- [ ] Integration with IDR command line tools
- [ ] Support for IDR-to-IDR transformations
- [ ] Advanced pipeline optimization
- [ ] Custom template support
