"""
Pipeline Generator for IDR XML Mapping Generator
Based on IDR User Guide 2024Q4 SP1 lines 4739-4750
"""

from typing import List, Dict, Optional
import logging
from core.schema_parser import SchemaAnalysis
from core.idr_types import (
    IDRPipeline, PipelineStage, StreamVariable, Assignment, Filter, TimeRange, Target,
    CoreStreamType, AssignmentSourceType, IDRFact, IDRDimension,
    GenerationConfig, IDRNamingConventions
)

logger = logging.getLogger(__name__)


class PipelineGenerator:
    """
    Generates IDR Pipeline definitions for Stream mode
    Based on User Guide examples from lines 4739-4750
    """
    
    def __init__(self, config: GenerationConfig):
        self.config = config
        self.naming = IDRNamingConventions()
    
    def generate_pipelines(self, schema_analysis: SchemaAnalysis, 
                         facts: List[IDRFact], dimensions: List[IDRDimension]) -> List[IDRPipeline]:
        """Generate pipelines for the transformation"""
        pipelines = []
        
        # Create main data pipeline
        main_pipeline = self._create_main_pipeline(schema_analysis, facts, dimensions)
        if main_pipeline:
            pipelines.append(main_pipeline)
        
        # Create validation pipeline if enabled
        if self.config.enable_validation:
            validation_pipeline = self._create_validation_pipeline(facts, dimensions)
            if validation_pipeline:
                pipelines.append(validation_pipeline)
        
        logger.info(f"Generated {len(pipelines)} pipelines")
        return pipelines
    
    def _create_main_pipeline(self, schema_analysis: SchemaAnalysis,
                            facts: List[IDRFact], dimensions: List[IDRDimension]) -> Optional[IDRPipeline]:
        """Create the main data processing pipeline"""
        try:
            # Create initial stage
            initial_stage = self._create_initial_stage(schema_analysis, facts, dimensions)
            if not initial_stage:
                return None
            
            pipeline_name = "MainDataPipeline"
            
            return IDRPipeline(
                name=pipeline_name,
                initial_stage=initial_stage,
                additional_stages=[],
                validation_enabled=self.config.enable_validation
            )
            
        except Exception as e:
            logger.error(f"Error creating main pipeline: {e}")
            return None
    
    def _create_initial_stage(self, schema_analysis: SchemaAnalysis,
                            facts: List[IDRFact], dimensions: List[IDRDimension]) -> Optional[PipelineStage]:
        """Create the initial stage of the pipeline"""
        try:
            stage_name = "InitialStage"
            
            # Determine core stream type
            core_stream = self._determine_core_stream(schema_analysis)
            
            # Create time range
            time_range = self._create_time_range(core_stream)
            
            # Create filters
            filters = self._create_default_filters()
            
            # Create stream variables
            stream_variables = self._create_stream_variables(schema_analysis, facts)
            
            # Create assignments
            assignments = self._create_assignments(schema_analysis, facts, dimensions)
            
            # Create targets
            targets = self._create_targets(facts)
            
            return PipelineStage(
                name=stage_name,
                core_stream=core_stream,
                time_range=time_range,
                filters=filters,
                stream_variables=stream_variables,
                assignments=assignments,
                targets=targets
            )
            
        except Exception as e:
            logger.error(f"Error creating initial stage: {e}")
            return None
    
    def _determine_core_stream(self, schema_analysis: SchemaAnalysis) -> CoreStreamType:
        """Determine appropriate core stream type"""
        # Use configuration default or analyze schema to determine best fit
        if hasattr(self.config, 'default_core_stream'):
            return self.config.default_core_stream
        
        # Simple heuristic: if schema has time-related elements, use time-based streams
        time_indicators = ['date', 'time', 'month', 'year', 'period']
        
        for element in schema_analysis.elements:
            element_name_lower = element.name.lower()
            if any(indicator in element_name_lower for indicator in time_indicators):
                return CoreStreamType.PRJM  # Monthly projections
        
        # Default to IMP for individual model points
        return CoreStreamType.IMP
    
    def _create_time_range(self, core_stream: CoreStreamType) -> Optional[TimeRange]:
        """Create time range based on core stream type"""
        # Time range is required for projection streams
        if core_stream in [CoreStreamType.PRJM, CoreStreamType.PRJY, 
                          CoreStreamType.STOM, CoreStreamType.STOY]:
            return TimeRange(first=0, times=120)  # 10 years monthly
        
        # IMP and other streams typically don't need time range
        return None
    
    def _create_default_filters(self) -> List[Filter]:
        """Create default filters for the pipeline"""
        filters = [
            Filter(filter_type="RunNumbers", values=["1"]),
            Filter(filter_type="Connections", values=["S1"])
        ]
        return filters
    
    def _create_stream_variables(self, schema_analysis: SchemaAnalysis, 
                               facts: List[IDRFact]) -> List[StreamVariable]:
        """Create stream variables from schema elements"""
        stream_variables = []
        
        # Create stream variables for fact variables that come from source
        for fact in facts:
            for variable in fact.variables:
                # Skip standard IDR variables (they come from key)
                if variable.name in ['JobId', 'RunNumber', 'SPCode', 'MonthId']:
                    continue
                
                # Skip dimension foreign keys
                if variable.name.endswith('Id') and variable.type == 'int':
                    continue
                
                # Create stream variable for data variables
                stream_var = StreamVariable(
                    variable_name=variable.name,
                    variable_type=variable.type,
                    source_variable=self._map_to_source_variable(variable.name),
                    autoassign=True
                )
                stream_variables.append(stream_var)
        
        return stream_variables
    
    def _map_to_source_variable(self, variable_name: str) -> str:
        """Map IDR variable name to source variable name"""
        # Simple mapping - in real implementation, this would be more sophisticated
        # For Prophet sources, many variables have the same name
        
        # Common Prophet variable mappings
        prophet_mappings = {
            'DEATH_OUTGO': 'DEATH_OUTGO',
            'SURR_OUTGO': 'SURR_OUTGO', 
            'ANNUAL_PREM': 'ANNUAL_PREM',
            'RESERVES': 'RESERVES',
            'CASH_FLOW': 'CASH_FLOW',
            'PRESENT_VALUE': 'PRESENT_VALUE',
            'NEW_BUSINESS': 'NEW_BUSINESS',
            'POLICY_COUNT': 'POLICY_COUNT'
        }
        
        return prophet_mappings.get(variable_name, variable_name)
    
    def _create_assignments(self, schema_analysis: SchemaAnalysis,
                          facts: List[IDRFact], dimensions: List[IDRDimension]) -> List[Assignment]:
        """Create assignments for the pipeline"""
        assignments = []
        
        # Create standard key assignments
        standard_assignments = [
            Assignment(
                variable_name="JobId",
                variable_type="int",
                source_type=AssignmentSourceType.KEY,
                source_value="JobId"
            ),
            Assignment(
                variable_name="RunNumber",
                variable_type="int",
                source_type=AssignmentSourceType.KEY,
                source_value="RunNumber"
            ),
            Assignment(
                variable_name="SPCode",
                variable_type="int",
                source_type=AssignmentSourceType.KEY,
                source_value="SPCode"
            ),
            Assignment(
                variable_name="MonthId",
                variable_type="int",
                source_type=AssignmentSourceType.KEY,
                source_value="Time"
            )
        ]
        assignments.extend(standard_assignments)
        
        # Create dimension lookup assignments
        for dimension in dimensions:
            # Skip standard dimensions that are handled above
            if dimension.name in ['DimTime', 'DimRun', 'DimSPCode']:
                continue
            
            assignment = Assignment(
                variable_name=dimension.id_column,
                variable_type="int",
                source_type=AssignmentSourceType.KEY,  # Dimension lookup
                source_value=dimension.key_column
            )
            assignments.append(assignment)
        
        # Create stream variable assignments (these are auto-created if autoassign=true)
        # But we can create explicit ones for complex mappings
        
        return assignments
    
    def _create_targets(self, facts: List[IDRFact]) -> List[Target]:
        """Create targets for the pipeline"""
        targets = []
        
        for fact in facts:
            target = Target(
                fact_name=fact.name,
                filter_expression=None,  # No filter by default
                skip_when_zero=False
            )
            targets.append(target)
        
        return targets
    
    def _create_validation_pipeline(self, facts: List[IDRFact], 
                                  dimensions: List[IDRDimension]) -> Optional[IDRPipeline]:
        """Create validation pipeline using ~~Validation~~ temporary table"""
        try:
            # Create validation stage
            validation_stage = PipelineStage(
                name="ValidationStage",
                core_stream=CoreStreamType.IMP,
                time_range=TimeRange(first=0, times=1),
                filters=[Filter(filter_type="RunNumbers", values=["1"])],
                stream_variables=[
                    StreamVariable(
                        variable_name="PolicyNumber",
                        variable_type="string",
                        source_variable="Asset_Name",
                        autoassign=False
                    )
                ],
                assignments=[
                    Assignment(
                        variable_name="Key",
                        variable_type="string",
                        source_type=AssignmentSourceType.EXPRESSION,
                        source_value='"Validation: Data Quality Check"'
                    ),
                    Assignment(
                        variable_name="Level",
                        variable_type="string",
                        source_type=AssignmentSourceType.GLOBAL,
                        source_value="INFO"
                    ),
                    Assignment(
                        variable_name="Message",
                        variable_type="string",
                        source_type=AssignmentSourceType.EXPRESSION,
                        source_value='"Pipeline validation completed successfully"'
                    )
                ],
                targets=[
                    Target(
                        fact_name="~~Validation~~",
                        filter_expression=None
                    )
                ]
            )
            
            return IDRPipeline(
                name="ValidationPipeline",
                initial_stage=validation_stage,
                additional_stages=[],
                validation_enabled=True
            )
            
        except Exception as e:
            logger.error(f"Error creating validation pipeline: {e}")
            return None
    
    def create_multi_stage_pipeline(self, facts: List[IDRFact], 
                                  grouping_fields: List[str]) -> Optional[IDRPipeline]:
        """Create multi-stage pipeline with grouping"""
        try:
            # Create initial stage
            initial_stage = PipelineStage(
                name="InitialStage",
                core_stream=CoreStreamType.PRJM,
                time_range=TimeRange(first=0, times=120),
                filters=[Filter(filter_type="RunNumbers", values=["1"])],
                stream_variables=[
                    StreamVariable("I_1", "double", "I_1", autoassign=True),
                    StreamVariable("I_2", "double", "I_2", autoassign=True)
                ],
                assignments=[
                    Assignment("RunNumber", "int", AssignmentSourceType.KEY, "RunNumber"),
                    Assignment("SPCode", "int", AssignmentSourceType.KEY, "SPCode"),
                    Assignment("Time", "int", AssignmentSourceType.KEY, "Time")
                ],
                targets=[]
            )
            
            # Create grouping stage
            grouping_stage = PipelineStage(
                name="GroupingStage",
                key_properties=[
                    {"source": "key", "type": "int", "name": field} 
                    for field in grouping_fields
                ],
                stream_variables=[
                    StreamVariable("I_1", "double", "I_1", autoassign=False, aggregate_function="sum"),
                    StreamVariable("I_2", "double", "I_2", autoassign=False, aggregate_function="max")
                ],
                assignments=[
                    Assignment("I_1", "double", AssignmentSourceType.STREAM, "I_1"),
                    Assignment("I_2", "double", AssignmentSourceType.STREAM, "I_2")
                ],
                targets=[Target(fact_name=facts[0].name if facts else "F1")]
            )
            
            return IDRPipeline(
                name="MultiStagePipeline",
                initial_stage=initial_stage,
                additional_stages=[grouping_stage],
                validation_enabled=False
            )
            
        except Exception as e:
            logger.error(f"Error creating multi-stage pipeline: {e}")
            return None
