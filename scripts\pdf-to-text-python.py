#!/usr/bin/env python3
"""
PDF to Text Converter for IDR User Guide
Supports multiple extraction methods for best results
"""

import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages if not available"""
    try:
        import subprocess
        packages = ['PyPDF2', 'pdfplumber', 'pymupdf']
        for package in packages:
            try:
                __import__(package.lower().replace('-', '_'))
            except ImportError:
                print(f"Installing {package}...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
    except Exception as e:
        print(f"Error installing packages: {e}")
        print("Please install manually: pip install PyPDF2 pdfplumber pymupdf")

def extract_with_pypdf2(pdf_path, output_path):
    """Extract text using PyPDF2 - good for simple PDFs"""
    try:
        import PyPDF2
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            
            for page_num, page in enumerate(pdf_reader.pages):
                print(f"Processing page {page_num + 1}/{len(pdf_reader.pages)}")
                text += f"\n--- PAGE {page_num + 1} ---\n"
                text += page.extract_text()
                text += "\n"
        
        with open(output_path, 'w', encoding='utf-8') as output_file:
            output_file.write(text)
        
        print(f"✓ PyPDF2 extraction completed: {output_path}")
        return True
        
    except Exception as e:
        print(f"✗ PyPDF2 extraction failed: {e}")
        return False

def extract_with_pdfplumber(pdf_path, output_path):
    """Extract text using pdfplumber - better for complex layouts"""
    try:
        import pdfplumber
        
        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                print(f"Processing page {page_num + 1}/{len(pdf.pages)}")
                text += f"\n--- PAGE {page_num + 1} ---\n"
                page_text = page.extract_text()
                if page_text:
                    text += page_text
                text += "\n"
        
        with open(output_path, 'w', encoding='utf-8') as output_file:
            output_file.write(text)
        
        print(f"✓ pdfplumber extraction completed: {output_path}")
        return True
        
    except Exception as e:
        print(f"✗ pdfplumber extraction failed: {e}")
        return False

def extract_with_pymupdf(pdf_path, output_path):
    """Extract text using PyMuPDF (fitz) - excellent for technical documents"""
    try:
        import fitz  # PyMuPDF
        
        doc = fitz.open(pdf_path)
        text = ""
        
        for page_num in range(len(doc)):
            print(f"Processing page {page_num + 1}/{len(doc)}")
            page = doc.load_page(page_num)
            text += f"\n--- PAGE {page_num + 1} ---\n"
            text += page.get_text()
            text += "\n"
        
        doc.close()
        
        with open(output_path, 'w', encoding='utf-8') as output_file:
            output_file.write(text)
        
        print(f"✓ PyMuPDF extraction completed: {output_path}")
        return True
        
    except Exception as e:
        print(f"✗ PyMuPDF extraction failed: {e}")
        return False

def main():
    if len(sys.argv) < 2:
        print("Usage: python pdf-to-text-python.py <pdf_path> [output_path]")
        print("Example: python pdf-to-text-python.py Documentation/IDR_User_Guide_2024Q4_SP1.pdf")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else str(Path(pdf_path).with_suffix('.txt'))
    
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found: {pdf_path}")
        sys.exit(1)
    
    print(f"Converting PDF to text...")
    print(f"Input: {pdf_path}")
    print(f"Output: {output_path}")
    print()
    
    # Try multiple extraction methods for best results
    methods = [
        ("PyMuPDF (fitz)", extract_with_pymupdf),
        ("pdfplumber", extract_with_pdfplumber),
        ("PyPDF2", extract_with_pypdf2)
    ]
    
    for method_name, method_func in methods:
        print(f"Trying {method_name}...")
        if method_func(pdf_path, output_path):
            print(f"\n✓ Successfully converted using {method_name}")
            print(f"Text file saved to: {output_path}")
            break
        print()
    else:
        print("✗ All extraction methods failed. Please try manual copy/paste or online tools.")

if __name__ == "__main__":
    # Uncomment the next line if you want to auto-install packages
    # install_requirements()
    main()
