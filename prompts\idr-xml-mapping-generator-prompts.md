# IDR XML Mapping Generator Prompts
## Using IDR User Guide 2024Q4 SP1 for IDR Stream Mode Implementation

### 1. Architecture and Design Prompts

#### Prompt 1.1: Understanding IDR Stream Mode Architecture
```
Based on the IDR User Guide 2024Q4 SP1, please explain:
1. What is IDR Stream mode and how does it differ from other IDR processing modes?
2. What are the key components and architecture patterns for IDR Stream mode?
3. What are the performance characteristics and use cases for Stream mode?
4. What are the technical requirements and dependencies for implementing Stream mode?

Please provide specific references to sections in the User Guide and include any code examples or configuration patterns mentioned.
```

#### Prompt 1.2: XML Mapping Requirements Analysis
```
From the IDR User Guide 2024Q4 SP1, extract information about:
1. XML schema requirements for IDR Stream mode
2. Supported XML mapping patterns and transformations
3. Data type mappings between XML and IDR internal formats
4. Validation rules and constraints for XML mappings
5. Error handling patterns for XML processing in Stream mode

Focus on sections related to XML processing, data mapping, and Stream mode specifications.
```

### 2. Implementation Planning Prompts

#### Prompt 2.1: Stream Mode Configuration
```
Using the IDR User Guide 2024Q4 SP1, help me understand:
1. How to configure IDR Stream mode for XML processing
2. What configuration parameters are available for Stream mode
3. How to set up XML input/output streams
4. Performance tuning options for Stream mode XML processing
5. Memory management considerations for large XML files

Please include specific configuration examples and best practices from the guide.
```

#### Prompt 2.2: XML Mapping Generator Design
```
Based on the IDR User Guide 2024Q4 SP1, design a system that can:
1. Automatically generate XML mappings for IDR Stream mode
2. Parse XML schemas and create corresponding IDR mapping configurations
3. Handle complex XML structures (nested elements, attributes, namespaces)
4. Generate validation rules based on XML schema constraints
5. Create transformation logic for data type conversions

What patterns and APIs does the User Guide recommend for this type of automation?
```

### 3. Technical Implementation Prompts

#### Prompt 3.1: Stream Processing Implementation
```
From the IDR User Guide 2024Q4 SP1, extract technical details for:
1. How to implement streaming XML processing in IDR
2. APIs and interfaces available for Stream mode
3. Event-driven processing patterns for XML streams
4. Memory-efficient processing of large XML documents
5. Error recovery and fault tolerance in Stream mode

Include code examples and API references from the documentation.
```

#### Prompt 3.2: Mapping Configuration Generation
```
Using the IDR User Guide 2024Q4 SP1, help me implement:
1. Automatic generation of IDR mapping configurations from XML schemas
2. Translation of XML Schema (XSD) elements to IDR field definitions
3. Handling of XML namespaces in IDR mappings
4. Generation of transformation rules for complex data types
5. Creation of validation logic based on schema constraints

What tools or utilities does the guide mention for mapping generation?
```

### 4. Testing and Validation Prompts

#### Prompt 4.1: Testing Stream Mode XML Processing
```
Based on the IDR User Guide 2024Q4 SP1, outline:
1. Testing strategies for IDR Stream mode XML processing
2. Performance testing approaches for large XML files
3. Validation testing for XML mapping accuracy
4. Error condition testing and edge cases
5. Integration testing with external XML sources

What testing tools or frameworks does the guide recommend?
```

#### Prompt 4.2: Validation and Quality Assurance
```
From the IDR User Guide 2024Q4 SP1, extract information about:
1. How to validate XML mappings before deployment
2. Quality assurance processes for Stream mode implementations
3. Monitoring and logging best practices
4. Performance benchmarking for XML processing
5. Troubleshooting common issues in Stream mode

Include specific metrics and monitoring approaches mentioned in the guide.
```

### 5. Advanced Features and Optimization Prompts

#### Prompt 5.1: Advanced XML Processing Features
```
Using the IDR User Guide 2024Q4 SP1, explore:
1. Advanced XML processing capabilities in IDR Stream mode
2. Support for XML transformations and XSLT processing
3. Handling of large XML documents with streaming
4. Parallel processing options for XML streams
5. Integration with external XML processing libraries

What advanced features are available for complex XML scenarios?
```

#### Prompt 5.2: Performance Optimization
```
Based on the IDR User Guide 2024Q4 SP1, identify:
1. Performance optimization techniques for Stream mode XML processing
2. Memory management strategies for large XML files
3. Caching mechanisms for frequently used mappings
4. Parallel processing configurations
5. Resource allocation and tuning parameters

Include specific performance tuning recommendations from the documentation.
```

### 6. Integration and Deployment Prompts

#### Prompt 6.1: System Integration
```
From the IDR User Guide 2024Q4 SP1, understand:
1. How to integrate IDR Stream mode with existing systems
2. API endpoints and interfaces for XML mapping services
3. Security considerations for XML processing
4. Authentication and authorization for Stream mode access
5. Deployment patterns and best practices

What integration patterns does the guide recommend?
```

#### Prompt 6.2: Production Deployment
```
Using the IDR User Guide 2024Q4 SP1, plan for:
1. Production deployment of IDR Stream mode XML processing
2. Monitoring and alerting for XML mapping services
3. Backup and recovery procedures
4. Scaling strategies for high-volume XML processing
5. Maintenance and update procedures

Include operational considerations and best practices from the guide.
```

## Usage Instructions

1. Use these prompts with the IDR User Guide 2024Q4 SP1 PDF
2. Each prompt is designed to extract specific information from the documentation
3. Combine the responses to build a comprehensive understanding of IDR Stream mode
4. Use the extracted information to design and implement your XML mapping generator
5. Iterate through the prompts as you develop different components of the system

## Next Steps

After using these prompts with the User Guide:
1. Create a detailed technical specification
2. Design the system architecture
3. Implement core components
4. Develop testing strategies
5. Plan deployment and integration
