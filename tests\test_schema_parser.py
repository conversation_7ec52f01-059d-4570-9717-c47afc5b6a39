"""
Tests for XSD Schema Parser
"""

import pytest
import tempfile
import os
from pathlib import Path

from src.core.schema_parser import <PERSON>SD<PERSON><PERSON><PERSON>, TypeMapper, XSDType, IDRType


class TestTypeMapper:
    """Test type mapping functionality"""
    
    def test_xsd_to_idr_mapping(self):
        """Test XSD to IDR type mapping"""
        assert TypeMapper.map_xsd_to_idr("string") == "string"
        assert TypeMapper.map_xsd_to_idr("int") == "int"
        assert TypeMapper.map_xsd_to_idr("integer") == "int"
        assert TypeMapper.map_xsd_to_idr("decimal") == "double"
        assert TypeMapper.map_xsd_to_idr("double") == "double"
        assert TypeMapper.map_xsd_to_idr("boolean") == "bool"
        assert TypeMapper.map_xsd_to_idr("date") == "string"
    
    def test_unknown_type_mapping(self):
        """Test mapping of unknown types"""
        assert TypeMapper.map_xsd_to_idr("unknownType") == "string"
        assert TypeMapper.map_xsd_to_idr("xs:customType") == "string"
    
    def test_namespace_prefix_handling(self):
        """Test handling of namespace prefixes"""
        assert TypeMapper.map_xsd_to_idr("xs:string") == "string"
        assert TypeMapper.map_xsd_to_idr("xsd:int") == "int"
        assert TypeMapper.map_xsd_to_idr("custom:decimal") == "double"
    
    def test_default_values(self):
        """Test default value generation"""
        assert TypeMapper.get_default_value("string") == '""'
        assert TypeMapper.get_default_value("int") == "0"
        assert TypeMapper.get_default_value("double") == "0.0"
        assert TypeMapper.get_default_value("bool") == "false"


class TestXSDParser:
    """Test XSD parsing functionality"""
    
    @pytest.fixture
    def sample_xsd_content(self):
        """Sample XSD content for testing"""
        return '''<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://example.com/test"
           xmlns:tns="http://example.com/test"
           elementFormDefault="qualified">
           
  <xs:element name="Product" type="tns:ProductType"/>
  
  <xs:complexType name="ProductType">
    <xs:sequence>
      <xs:element name="ProductId" type="xs:int"/>
      <xs:element name="ProductName" type="xs:string"/>
      <xs:element name="Price" type="xs:decimal"/>
      <xs:element name="IsActive" type="xs:boolean"/>
    </xs:sequence>
    <xs:attribute name="category" type="xs:string" use="optional"/>
  </xs:complexType>
  
  <xs:element name="Customer" type="tns:CustomerType"/>
  
  <xs:complexType name="CustomerType">
    <xs:sequence>
      <xs:element name="CustomerId" type="xs:int"/>
      <xs:element name="CustomerName" type="xs:string"/>
      <xs:element name="Email" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  
</xs:schema>'''
    
    @pytest.fixture
    def temp_xsd_file(self, sample_xsd_content):
        """Create temporary XSD file"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xsd', delete=False) as f:
            f.write(sample_xsd_content)
            temp_file = f.name
        
        yield temp_file
        
        # Cleanup
        os.unlink(temp_file)
    
    def test_parse_schema(self, temp_xsd_file):
        """Test basic schema parsing"""
        parser = XSDParser()
        analysis = parser.parse_schema(temp_xsd_file)
        
        assert analysis is not None
        assert analysis.target_namespace == "http://example.com/test"
        assert len(analysis.elements) > 0
        assert len(analysis.complex_types) > 0
    
    def test_extract_namespaces(self, temp_xsd_file):
        """Test namespace extraction"""
        parser = XSDParser()
        analysis = parser.parse_schema(temp_xsd_file)
        
        assert 'xs' in analysis.namespaces
        assert 'tns' in analysis.namespaces
        assert analysis.namespaces['xs'] == "http://www.w3.org/2001/XMLSchema"
    
    def test_parse_elements(self, temp_xsd_file):
        """Test element parsing"""
        parser = XSDParser()
        analysis = parser.parse_schema(temp_xsd_file)
        
        # Find Product element
        product_elements = [e for e in analysis.elements if e.name == "Product"]
        assert len(product_elements) > 0
        
        # Find ProductId element
        product_id_elements = [e for e in analysis.elements if e.name == "ProductId"]
        assert len(product_id_elements) > 0
        
        product_id = product_id_elements[0]
        assert product_id.type in ["xs:int", "int"]
        assert product_id.min_occurs == 1
        assert product_id.max_occurs == 1
    
    def test_parse_complex_types(self, temp_xsd_file):
        """Test complex type parsing"""
        parser = XSDParser()
        analysis = parser.parse_schema(temp_xsd_file)
        
        # Find ProductType
        product_types = [ct for ct in analysis.complex_types if ct.name == "ProductType"]
        assert len(product_types) > 0
        
        product_type = product_types[0]
        assert len(product_type.elements) >= 4  # ProductId, ProductName, Price, IsActive
        assert len(product_type.attributes) >= 1  # category attribute
        
        # Check element names
        element_names = [e.name for e in product_type.elements]
        assert "ProductId" in element_names
        assert "ProductName" in element_names
        assert "Price" in element_names
        assert "IsActive" in element_names
        
        # Check attribute
        attr_names = [a.name for a in product_type.attributes]
        assert "category" in attr_names
    
    def test_find_root_elements(self, temp_xsd_file):
        """Test root element identification"""
        parser = XSDParser()
        analysis = parser.parse_schema(temp_xsd_file)
        
        root_element_names = [e.name for e in analysis.root_elements]
        assert "Product" in root_element_names or "Customer" in root_element_names
    
    def test_invalid_xsd_file(self):
        """Test handling of invalid XSD file"""
        parser = XSDParser()
        
        with pytest.raises(Exception):
            parser.parse_schema("nonexistent_file.xsd")
    
    def test_malformed_xsd(self):
        """Test handling of malformed XSD"""
        malformed_content = '''<?xml version="1.0"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="Test" type="xs:string"
  <!-- Missing closing tag -->
</xs:schema>'''
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xsd', delete=False) as f:
            f.write(malformed_content)
            temp_file = f.name
        
        try:
            parser = XSDParser()
            with pytest.raises(Exception):
                parser.parse_schema(temp_file)
        finally:
            os.unlink(temp_file)


class TestConstraintAnalyzer:
    """Test constraint analysis functionality"""
    
    @pytest.fixture
    def constrained_xsd_content(self):
        """XSD content with constraints"""
        return '''<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
  
  <xs:element name="Product">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="ProductCode">
          <xs:simpleType>
            <xs:restriction base="xs:string">
              <xs:minLength value="3"/>
              <xs:maxLength value="10"/>
              <xs:pattern value="[A-Z]{3}[0-9]{3,7}"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
        <xs:element name="Price">
          <xs:simpleType>
            <xs:restriction base="xs:decimal">
              <xs:minInclusive value="0.01"/>
              <xs:maxInclusive value="999999.99"/>
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
        <xs:element name="Description" type="xs:string" minOccurs="0"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  
</xs:schema>'''
    
    @pytest.fixture
    def temp_constrained_xsd(self, constrained_xsd_content):
        """Create temporary XSD file with constraints"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xsd', delete=False) as f:
            f.write(constrained_xsd_content)
            temp_file = f.name
        
        yield temp_file
        os.unlink(temp_file)
    
    def test_constraint_analysis(self, temp_constrained_xsd):
        """Test constraint analysis"""
        from src.core.schema_parser import ConstraintAnalyzer
        
        parser = XSDParser()
        analysis = parser.parse_schema(temp_constrained_xsd)
        
        analyzer = ConstraintAnalyzer()
        rules = analyzer.analyze_constraints(analysis)
        
        assert len(rules) > 0
        
        # Check for specific constraint types
        rule_types = [rule['type'] for rule in rules]
        assert 'minLength' in rule_types or 'maxLength' in rule_types
        assert 'minValue' in rule_types or 'maxValue' in rule_types
    
    def test_required_field_detection(self, temp_constrained_xsd):
        """Test required field detection"""
        from src.core.schema_parser import ConstraintAnalyzer
        
        parser = XSDParser()
        analysis = parser.parse_schema(temp_constrained_xsd)
        
        analyzer = ConstraintAnalyzer()
        rules = analyzer.analyze_constraints(analysis)
        
        # Description is optional (minOccurs="0"), others are required
        required_rules = [rule for rule in rules if rule['type'] == 'required']
        
        # Should have required rules for ProductCode and Price, but not Description
        required_fields = [rule['field'] for rule in required_rules]
        assert 'ProductCode' in required_fields
        assert 'Price' in required_fields
        # Description should not be required
        assert 'Description' not in required_fields or any(
            rule['field'] == 'Description' and rule['type'] != 'required' 
            for rule in rules
        )
