# Technical Implementation Prompts for IDR XML Mapping Generator

## Code Generation and Automation Prompts

### Prompt T1: XML Schema Analysis
```
Using the IDR User Guide 2024Q4 SP1, help me create a system that can:

1. Parse XML Schema Definition (XSD) files and extract:
   - Element definitions and their data types
   - Attribute specifications
   - Complex type definitions
   - Namespace declarations
   - Validation constraints (min/max values, patterns, etc.)

2. Map XSD constructs to IDR Stream mode equivalents:
   - Simple types to IDR field types
   - Complex types to IDR record structures
   - Attributes to IDR field attributes
   - Constraints to IDR validation rules

What specific APIs, data structures, or configuration formats does the User Guide specify for this type of schema analysis in Stream mode?
```

### Prompt T2: Mapping Configuration Generator
```
Based on the IDR User Guide 2024Q4 SP1, design a generator that creates:

1. IDR mapping configuration files from XML schemas
2. Field-level mappings with proper data type conversions
3. Nested structure handling for complex XML hierarchies
4. Namespace resolution and prefix management
5. Default value assignments and null handling

What is the exact format and syntax for IDR Stream mode mapping configurations as specified in the User Guide? Include examples of:
- Simple field mappings
- Complex nested structures
- Array/list handling
- Conditional mappings
```

### Prompt T3: Stream Processing Pipeline
```
From the IDR User Guide 2024Q4 SP1, extract details for implementing:

1. XML stream reader configuration for IDR Stream mode
2. Event-driven processing handlers for XML elements
3. Memory-efficient processing of large XML documents
4. Error handling and recovery mechanisms
5. Progress tracking and monitoring for long-running processes

What specific classes, interfaces, or APIs does the guide mention for Stream mode XML processing? Include initialization patterns and lifecycle management.
```

## Data Transformation Prompts

### Prompt T4: Data Type Mapping
```
Using the IDR User Guide 2024Q4 SP1, create comprehensive mappings for:

1. XML Schema primitive types to IDR data types:
   - string, int, decimal, boolean, date, time, dateTime
   - Custom restrictions and patterns
   - Union types and choice elements

2. Complex data transformations:
   - XML attributes to IDR fields
   - Mixed content handling
   - CDATA sections
   - Processing instructions

What are the exact data type mappings and conversion rules specified in the User Guide for Stream mode?
```

### Prompt T5: Validation Rule Generation
```
Based on the IDR User Guide 2024Q4 SP1, implement validation logic for:

1. XML Schema constraints in IDR format:
   - minLength, maxLength, pattern restrictions
   - minInclusive, maxInclusive, minExclusive, maxExclusive
   - enumeration values
   - totalDigits, fractionDigits

2. Structural validations:
   - Required vs optional elements
   - Occurrence constraints (minOccurs, maxOccurs)
   - Choice and sequence validations
   - Unique and key constraints

How does the User Guide specify validation rule configuration for Stream mode processing?
```

## Performance and Optimization Prompts

### Prompt T6: Memory Management
```
From the IDR User Guide 2024Q4 SP1, understand:

1. Memory management strategies for Stream mode XML processing:
   - Buffer size configuration
   - Garbage collection considerations
   - Memory pooling for frequent operations
   - Large document streaming techniques

2. Resource optimization:
   - Connection pooling for multiple XML sources
   - Caching strategies for mapping configurations
   - Parallel processing configuration
   - Thread safety considerations

What specific memory management and performance tuning options are available in IDR Stream mode?
```

### Prompt T7: Error Handling and Recovery
```
Using the IDR User Guide 2024Q4 SP1, design error handling for:

1. XML parsing errors:
   - Malformed XML handling
   - Schema validation failures
   - Encoding issues
   - Namespace resolution errors

2. Processing errors:
   - Data type conversion failures
   - Mapping configuration errors
   - Resource exhaustion scenarios
   - Network connectivity issues

3. Recovery mechanisms:
   - Checkpoint and restart capabilities
   - Partial processing results
   - Error logging and reporting
   - Fallback processing modes

What error handling patterns and APIs does the User Guide recommend for robust Stream mode implementations?
```

## Integration and API Prompts

### Prompt T8: API Design
```
Based on the IDR User Guide 2024Q4 SP1, design APIs for:

1. XML mapping generator service:
   - Schema upload and analysis endpoints
   - Mapping configuration generation
   - Validation and testing interfaces
   - Configuration management

2. Stream processing service:
   - XML stream processing endpoints
   - Real-time processing status
   - Result retrieval and export
   - Monitoring and metrics

What API patterns and interfaces does the User Guide specify for Stream mode services?
```

### Prompt T9: Configuration Management
```
From the IDR User Guide 2024Q4 SP1, implement:

1. Mapping configuration storage and versioning:
   - Configuration file formats
   - Version control and change tracking
   - Environment-specific configurations
   - Configuration validation and testing

2. Runtime configuration management:
   - Dynamic configuration updates
   - Configuration caching and refresh
   - Multi-tenant configuration isolation
   - Configuration backup and restore

How does the User Guide recommend managing and organizing Stream mode configurations?
```

## Testing and Quality Assurance Prompts

### Prompt T10: Automated Testing Framework
```
Using the IDR User Guide 2024Q4 SP1, create testing strategies for:

1. Unit testing:
   - Schema parsing accuracy
   - Mapping generation correctness
   - Data transformation validation
   - Error handling verification

2. Integration testing:
   - End-to-end XML processing
   - Performance benchmarking
   - Stress testing with large files
   - Concurrent processing validation

3. Regression testing:
   - Configuration compatibility
   - API backward compatibility
   - Performance regression detection
   - Error handling consistency

What testing tools, frameworks, or utilities does the User Guide mention for Stream mode validation?
```

## Usage Guidelines

1. **Sequential Processing**: Use these prompts in order to build understanding progressively
2. **Documentation Reference**: Always reference specific sections of the IDR User Guide 2024Q4 SP1
3. **Code Examples**: Request concrete code examples and configuration samples
4. **Best Practices**: Ask for recommended patterns and anti-patterns
5. **Version Compatibility**: Ensure all recommendations are specific to the 2024Q4 SP1 version

## Expected Outcomes

After processing these prompts with the User Guide, you should have:
- Detailed technical specifications for each component
- Code examples and configuration templates
- Performance optimization strategies
- Comprehensive testing approaches
- Production deployment guidelines
