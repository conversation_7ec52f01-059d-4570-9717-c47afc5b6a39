<?xml version="1.0" encoding="utf-8"?>
<!-- IDR Stream Mode TransformDefinition Template -->
<!-- Based on IDR User Guide 2024Q4 SP1, lines 4201-4220 -->

<TransformDefinition>
  <Name>{TRANSFORM_NAME}</Name>
  
  <!-- Connections Section -->
  <Connections>
    <SourceConnections>
      <Connection>{SOURCE_CONNECTION_1}</Connection>
      <!-- Add more source connections as needed -->
    </SourceConnections>
    <TargetConnections>
      <Connection>{TARGET_CONNECTION_1}</Connection>
      <!-- Add more target connections as needed -->
    </TargetConnections>
  </Connections>

  <!-- Dimensions Section -->
  <Dimensions>
    <!-- Insert dimension definitions here -->
    {DIMENSIONS_XML}
  </Dimensions>

  <!-- Facts Section -->
  <Facts>
    <!-- Insert fact definitions here -->
    {FACTS_XML}
  </Facts>

  <!-- Temporary Tables Section (optional) -->
  <TemporaryTables>
    <!-- Insert temporary table definitions here if needed -->
    {TEMPORARY_TABLES_XML}
  </TemporaryTables>

  <!-- Source Streams Section (for IDR-to-IDR scenarios) -->
  <SourceStreams>
    <!-- Insert source stream definitions here if needed -->
    {SOURCE_STREAMS_XML}
  </SourceStreams>

  <!-- Pipelines Section -->
  <Pipelines>
    <Pipeline validate="{VALIDATION_ENABLED}">
      <Name>{PIPELINE_NAME}</Name>
      <InitialStage>
        <Name>{INITIAL_STAGE_NAME}</Name>
        <CoreStream>{CORE_STREAM_TYPE}</CoreStream>
        <TimeRange>
          <First>{TIME_FIRST}</First>
          <Times>{TIME_COUNT}</Times>
        </TimeRange>
        <Filters>
          {FILTERS_XML}
        </Filters>
        <StreamVariables>
          {STREAM_VARIABLES_XML}
        </StreamVariables>
        <Assignments>
          {ASSIGNMENTS_XML}
        </Assignments>
        <Targets>
          <Target>
            <Fact>{TARGET_FACT_NAME}</Fact>
            <Filter>{TARGET_FILTER}</Filter>
          </Target>
        </Targets>
      </InitialStage>
      
      <!-- Additional stages for multi-stage pipelines -->
      <Stages>
        {ADDITIONAL_STAGES_XML}
      </Stages>
    </Pipeline>
  </Pipelines>
</TransformDefinition>

<!-- 
Template Variables:
- {TRANSFORM_NAME}: Name of the transformation
- {SOURCE_CONNECTION_1}: Source connection name (e.g., "S1")
- {TARGET_CONNECTION_1}: Target connection name (e.g., "T1")
- {DIMENSIONS_XML}: Complete dimensions XML block
- {FACTS_XML}: Complete facts XML block
- {TEMPORARY_TABLES_XML}: Temporary tables XML (optional)
- {SOURCE_STREAMS_XML}: Source streams XML (for IDR sources)
- {VALIDATION_ENABLED}: "true" or "false"
- {PIPELINE_NAME}: Name of the pipeline
- {INITIAL_STAGE_NAME}: Name of the initial stage
- {CORE_STREAM_TYPE}: Type of core stream (PRJM, PRJY, STOM, STOY, IMP, etc.)
- {TIME_FIRST}: First time period (usually 0)
- {TIME_COUNT}: Number of time periods
- {FILTERS_XML}: Filter definitions
- {STREAM_VARIABLES_XML}: Stream variable definitions
- {ASSIGNMENTS_XML}: Assignment definitions
- {TARGET_FACT_NAME}: Name of target fact table
- {TARGET_FILTER}: Filter for target (optional)
- {ADDITIONAL_STAGES_XML}: Additional pipeline stages (optional)

Core Stream Types:
- PRJM: Monthly projections
- PRJY: Yearly projections  
- STOM: Monthly stochastic
- STOY: Yearly stochastic
- IMP: Individual model points
- IDRM: IDR monthly (for IDR-to-IDR)

Usage Notes:
- This is the root template for all Stream mode transformations
- All sections are required except TemporaryTables and SourceStreams
- Pipelines can have multiple stages for complex transformations
- validate="true" enables validation using ~~Validation~~ temporary table
-->
