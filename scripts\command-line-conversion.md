# Command Line PDF to Text Conversion

## Windows Command Line Options

### 1. Using pdftotext (Poppler Utils)

#### Installation:
```powershell
# Install using Chocolatey (if you have it)
choco install poppler

# Or download from: https://blog.alivate.com.au/poppler-windows/
# Extract to C:\poppler and add to PATH
```

#### Usage:
```powershell
# Basic conversion
pdftotext "Documentation\IDR_User_Guide_2024Q4_SP1.pdf" "IDR_User_Guide_2024Q4_SP1.txt"

# With layout preservation
pdftotext -layout "Documentation\IDR_User_Guide_2024Q4_SP1.pdf" "IDR_User_Guide_2024Q4_SP1.txt"

# Extract specific pages (e.g., pages 50-100)
pdftotext -f 50 -l 100 "Documentation\IDR_User_Guide_2024Q4_SP1.pdf" "IDR_Stream_Mode_Section.txt"
```

### 2. Using Windows Subsystem for Linux (WSL)

#### If you have WSL installed:
```bash
# Install poppler-utils in WSL
sudo apt update
sudo apt install poppler-utils

# Convert PDF
pdftotext "/mnt/c/Projects/Internal/IDRXMLMappingGenerator/Documentation/IDR_User_Guide_2024Q4_SP1.pdf" "/mnt/c/Projects/Internal/IDRXMLMappingGenerator/IDR_User_Guide_2024Q4_SP1.txt"
```

### 3. Using PowerShell with .NET (Advanced)

```powershell
# PowerShell script using iTextSharp (requires installation)
# Install-Package iTextSharp -Force

Add-Type -Path "path\to\itextsharp.dll"

$reader = New-Object iTextSharp.text.pdf.PdfReader("Documentation\IDR_User_Guide_2024Q4_SP1.pdf")
$text = ""

for ($page = 1; $page -le $reader.NumberOfPages; $page++) {
    $text += [iTextSharp.text.pdf.parser.PdfTextExtractor]::GetTextFromPage($reader, $page)
}

$text | Out-File "IDR_User_Guide_2024Q4_SP1.txt" -Encoding UTF8
$reader.Close()
```

## Quick Start Commands

### For Your Specific File:
```powershell
# Navigate to your project directory
cd "C:\Projects\Internal\IDRXMLMappingGenerator"

# Using Python (if installed)
python scripts\pdf-to-text-python.py "Documentation\IDR_User_Guide_2024Q4_SP1.pdf"

# Using PowerShell script
.\scripts\pdf-to-text-powershell.ps1 -PdfPath "Documentation\IDR_User_Guide_2024Q4_SP1.pdf"
```

## Quality Comparison

### Best Quality (Recommended Order):
1. **PyMuPDF (fitz)** - Excellent for technical documents
2. **pdfplumber** - Good layout preservation
3. **pdftotext with -layout** - Good command-line option
4. **Online tools (SmallPDF)** - Convenient but less secure
5. **PyPDF2** - Basic but reliable

### For IDR User Guide Specifically:
- Technical documentation often works best with **PyMuPDF** or **pdfplumber**
- These preserve code examples and formatting better
- Command-line tools are good for automation

## Troubleshooting

### Common Issues:
1. **"Module not found"** - Install required Python packages
2. **"Access denied"** - Run as administrator or check file permissions
3. **"Garbled text"** - PDF might be image-based, try OCR tools
4. **"Empty output"** - PDF might be protected, try different tool

### Solutions:
```powershell
# Install Python packages
pip install PyPDF2 pdfplumber pymupdf

# Check if PDF is text-based
python -c "import PyPDF2; print('Text-based PDF' if PyPDF2.PdfReader(open('Documentation/IDR_User_Guide_2024Q4_SP1.pdf', 'rb')).pages[0].extract_text() else 'Image-based PDF')"
```

## Recommended Workflow

### For Your IDR Project:
1. **Try Python script first** (best quality)
2. **Use online tool as backup** (SmallPDF)
3. **Manual extraction for critical sections** (copy/paste)
4. **Organize extracted text** by sections
5. **Use with the prompts** I created earlier

### Command to Run:
```powershell
# This should work for most cases
python scripts\pdf-to-text-python.py "Documentation\IDR_User_Guide_2024Q4_SP1.pdf" "IDR_User_Guide_Text.txt"
```
