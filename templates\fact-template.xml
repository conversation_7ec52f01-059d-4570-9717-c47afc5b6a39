<?xml version="1.0" encoding="utf-8"?>
<!-- IDR Stream Mode Fact Template -->
<!-- Based on IDR User Guide 2024Q4 SP1, lines 4317-4337 -->

<Facts>
  <Fact merge="{MERGE_ENABLED}">
    <Name>{FACT_NAME}</Name>
    <TableName>{TABLE_NAME}</TableName>
    <Connection>{CONNECTION}</Connection>
    <Variables>
      <!-- Standard dimension key variables -->
      <Variable type="int">JobId</Variable>
      <Variable type="int" match="{IS_MATCH_COLUMN}">{DIMENSION_ID_COLUMN}</Variable>
      <Variable type="int">RunNumber</Variable>
      <Variable type="int">SPCode</Variable>
      <Variable type="int">MonthId</Variable>
      
      <!-- Data variables - repeat for each data column -->
      <Variable type="{VARIABLE_TYPE}" match="{IS_MATCH_COLUMN}">{VARIABLE_NAME}</Variable>
    </Variables>
  </Fact>
</Facts>

<!-- 
Template Variables:
- {MERGE_ENABLED}: "true" or "false" (enables merge/upsert operations)
- {FACT_NAME}: Name used in stage targets (e.g., "F1")
- {TABLE_NAME}: Database table name (e.g., "MyFirstFactTable")
- {CONNECTION}: Connection name (e.g., "T1")
- {DIMENSION_ID_COLUMN}: Foreign key to dimension (e.g., "ProductId")
- {IS_MATCH_COLUMN}: "true" for columns used in merge matching
- {VARIABLE_TYPE}: Data type ("int", "double", "string", "bool", etc.)
- {VARIABLE_NAME}: Variable name (e.g., "MyVariableDirectFromProphetA")

Standard Variables (always included):
- JobId: Identifies the job/load
- RunNumber: Prophet run number
- SPCode: SP Code from Prophet
- MonthId: Time period identifier

Usage Notes:
- merge="true" enables upsert operations (insert or update)
- match="true" on variables indicates columns used for matching in merge
- Variable types must match database column types
- Names should match Prophet variable names or derived calculations
-->
