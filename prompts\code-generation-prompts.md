# Code Generation Prompts for IDR XML Mapping Generator

## Core Component Generation Prompts

### Prompt C1: XML Schema Parser
```
Based on the IDR User Guide 2024Q4 SP1, generate code for an XML Schema parser that:

1. **Input Processing**:
   - Reads XSD files and extracts schema definitions
   - Handles multiple namespace declarations
   - Processes import and include statements
   - Validates schema syntax and structure

2. **Schema Analysis**:
   - Identifies all element and attribute definitions
   - Extracts data type information and constraints
   - Maps complex types and inheritance relationships
   - Analyzes occurrence patterns (minOccurs, maxOccurs)

3. **IDR Integration**:
   - Uses IDR Stream mode APIs as specified in the User Guide
   - Follows IDR naming conventions and patterns
   - Implements proper error handling for IDR context
   - Integrates with IDR logging and monitoring systems

Please provide:
- Complete class structure with methods and properties
- Error handling specific to IDR Stream mode requirements
- Configuration options mentioned in the User Guide
- Integration points with other IDR components
```

### Prompt C2: Mapping Configuration Generator
```
Using the IDR User Guide 2024Q4 SP1, create code for generating IDR mapping configurations:

1. **Configuration Structure**:
   - Generate IDR-compliant mapping files from parsed schemas
   - Create field mappings with proper data type conversions
   - Handle nested structures and complex hierarchies
   - Generate validation rules and constraints

2. **Stream Mode Optimization**:
   - Optimize configurations for Stream mode processing
   - Implement memory-efficient mapping strategies
   - Configure streaming parameters as per User Guide
   - Set up proper buffering and batching options

3. **Template Generation**:
   - Create reusable mapping templates
   - Generate configuration variants for different environments
   - Implement configuration inheritance and overrides
   - Support for conditional and dynamic mappings

Include:
- Exact IDR configuration file format from the User Guide
- Stream mode specific parameters and settings
- Performance tuning options
- Validation and testing hooks
```

### Prompt C3: Stream Processor Implementation
```
From the IDR User Guide 2024Q4 SP1, implement a Stream processor for XML processing:

1. **Stream Processing Engine**:
   - Initialize IDR Stream mode as specified in the User Guide
   - Implement event-driven XML processing
   - Handle large file streaming with memory management
   - Process XML elements according to generated mappings

2. **Data Transformation**:
   - Convert XML data to IDR internal formats
   - Apply validation rules during processing
   - Handle data type conversions and formatting
   - Manage namespace resolution and prefix handling

3. **Error Recovery**:
   - Implement robust error handling for malformed XML
   - Provide checkpoint and restart capabilities
   - Log errors according to IDR standards
   - Support partial processing and recovery modes

Provide:
- Complete implementation using IDR Stream mode APIs
- Integration with IDR error handling framework
- Performance monitoring and metrics collection
- Configuration management for runtime parameters
```

## Utility and Helper Code Prompts

### Prompt C4: Data Type Converter
```
Based on the IDR User Guide 2024Q4 SP1, create a comprehensive data type converter:

1. **XML to IDR Conversions**:
   - Handle all XML Schema primitive types
   - Convert complex types and custom restrictions
   - Process union types and choice elements
   - Manage date/time format conversions

2. **Validation Integration**:
   - Apply IDR validation rules during conversion
   - Handle constraint violations gracefully
   - Provide detailed error messages for failures
   - Support custom validation extensions

3. **Performance Optimization**:
   - Cache frequently used conversion patterns
   - Optimize for Stream mode processing requirements
   - Minimize memory allocation during conversions
   - Support batch conversion operations

Include specific conversion logic for each data type mapping specified in the User Guide.
```

### Prompt C5: Configuration Manager
```
Using the IDR User Guide 2024Q4 SP1, implement a configuration management system:

1. **Configuration Storage**:
   - Store and retrieve mapping configurations
   - Support versioning and change tracking
   - Implement configuration validation and testing
   - Provide backup and restore capabilities

2. **Runtime Management**:
   - Load configurations dynamically during processing
   - Support hot-reload of configuration changes
   - Manage configuration caching and refresh
   - Handle multi-tenant configuration isolation

3. **IDR Integration**:
   - Use IDR configuration management APIs
   - Follow IDR security and access control patterns
   - Integrate with IDR monitoring and alerting
   - Support IDR deployment and lifecycle management

Provide implementation that follows IDR architectural patterns from the User Guide.
```

## Testing and Validation Code Prompts

### Prompt C6: Unit Test Framework
```
From the IDR User Guide 2024Q4 SP1, create comprehensive unit tests:

1. **Schema Parser Tests**:
   - Test parsing of various XSD constructs
   - Validate error handling for malformed schemas
   - Test namespace resolution and import handling
   - Verify complex type inheritance processing

2. **Mapping Generation Tests**:
   - Test configuration generation accuracy
   - Validate data type mapping correctness
   - Test constraint and validation rule generation
   - Verify nested structure handling

3. **Stream Processing Tests**:
   - Test XML processing with various file sizes
   - Validate memory usage and performance
   - Test error recovery and checkpoint functionality
   - Verify concurrent processing capabilities

Include test data sets and expected results based on User Guide examples.
```

### Prompt C7: Integration Test Suite
```
Based on the IDR User Guide 2024Q4 SP1, create integration tests:

1. **End-to-End Processing**:
   - Test complete XML processing pipeline
   - Validate integration with IDR Stream mode
   - Test performance with realistic data volumes
   - Verify output accuracy and completeness

2. **Error Scenario Testing**:
   - Test handling of various error conditions
   - Validate recovery mechanisms
   - Test partial processing scenarios
   - Verify error reporting and logging

3. **Performance Benchmarking**:
   - Create performance baseline tests
   - Test scalability with increasing data volumes
   - Validate memory usage patterns
   - Test concurrent processing limits

Provide test scenarios that match real-world usage patterns described in the User Guide.
```

## API and Interface Code Prompts

### Prompt C8: REST API Implementation
```
Using the IDR User Guide 2024Q4 SP1, implement REST APIs for:

1. **Schema Management**:
   - Upload and validate XML schemas
   - Generate and retrieve mapping configurations
   - Manage schema versions and dependencies
   - Provide schema analysis and documentation

2. **Processing Services**:
   - Submit XML files for processing
   - Monitor processing status and progress
   - Retrieve processing results and reports
   - Manage processing queues and priorities

3. **Configuration Services**:
   - Manage mapping configurations
   - Test and validate configurations
   - Deploy configurations to processing environments
   - Monitor configuration usage and performance

Follow IDR API design patterns and security requirements from the User Guide.
```

### Prompt C9: Command Line Interface
```
From the IDR User Guide 2024Q4 SP1, create a CLI tool for:

1. **Schema Operations**:
   - Parse and analyze XML schemas
   - Generate mapping configurations
   - Validate schema compatibility
   - Export schema documentation

2. **Processing Operations**:
   - Process XML files using generated mappings
   - Monitor processing progress
   - Generate processing reports
   - Manage processing configurations

3. **Administrative Operations**:
   - Manage system configurations
   - Monitor system health and performance
   - Backup and restore configurations
   - Update and maintain system components

Implement using IDR command-line patterns and conventions from the User Guide.
```

## Usage Instructions

1. **Preparation**: Have the IDR User Guide 2024Q4 SP1 readily available
2. **Context**: Provide relevant sections of the User Guide when using each prompt
3. **Iteration**: Use prompts iteratively, building on previous responses
4. **Validation**: Cross-reference generated code with User Guide specifications
5. **Testing**: Implement and test generated code incrementally

## Expected Deliverables

After using these prompts, you should have:
- Complete, working code components
- Comprehensive test suites
- API implementations
- Configuration management tools
- Documentation and usage guides

Each code component should be fully compatible with IDR Stream mode as specified in the 2024Q4 SP1 User Guide.
