# IDR XML Mapping Generator Prompts - Quick Reference

## Overview
This collection of prompts is designed to help you leverage the **IDR User Guide 2024Q4 SP1** to create a comprehensive IDR XML mapping generator using IDR Stream mode.

## File Structure
```
prompts/
├── README.md                           # This file - quick reference guide
├── idr-xml-mapping-generator-prompts.md # Main architectural and design prompts
├── technical-implementation-prompts.md  # Detailed technical implementation prompts
├── code-generation-prompts.md          # Specific code generation prompts
├── text-based-prompts.md              # Text-based prompts for working with converted PDF
├── xml-extraction-prompts.md          # Prompts for extracting XML examples from text
└── implementation-focused-prompts.md   # Production-ready implementation prompts
```

## How to Use These Prompts

### Step 1: Start with Text-Based Analysis (File 4)
Begin with `text-based-prompts.md` - **NEW: Optimized for converted PDF text**:
- Use Section 1 prompts for Stream mode core concepts with specific line references
- Use Section 2 prompts for technical implementation details
- Use Section 3-6 prompts for advanced features and testing
- **Advantage**: Direct line number references to the 17,706-line text file

### Step 2: Extract XML Examples (File 5)
Move to `xml-extraction-prompts.md` - **NEW: Extract actual XML from text**:
- Use Section 1 prompts to extract complete XML templates
- Use Section 2-4 prompts for specific XML patterns
- Use Section 5 prompts for complete working examples
- **Advantage**: Get real XML examples directly from the user guide

### Step 3: Implementation-Ready Code (File 6)
Use `implementation-focused-prompts.md` - **NEW: Production-ready implementation**:
- Use Section 1-2 prompts for architecture and core components
- Use Section 3-4 prompts for advanced features and integration
- Use Section 5 prompts for deployment and maintenance
- **Advantage**: Complete implementation guidance with code examples

### Alternative: Original Prompt Sets
If you prefer the original approach or don't have the text file:

#### Step A: Architecture (File 1)
Begin with `idr-xml-mapping-generator-prompts.md`:
- Use prompts 1.1-1.2 to understand IDR Stream mode fundamentals
- Use prompts 2.1-2.2 for implementation planning
- Progress through sections 3-6 for comprehensive coverage

#### Step B: Technical Implementation (File 2)
Move to `technical-implementation-prompts.md`:
- Use prompts T1-T3 for core technical understanding
- Use prompts T4-T5 for data transformation details
- Use prompts T6-T7 for performance and error handling
- Use prompts T8-T10 for integration and testing

#### Step C: Code Generation (File 3)
Use `code-generation-prompts.md`:
- Use prompts C1-C3 for core components
- Use prompts C4-C5 for utilities and helpers
- Use prompts C6-C7 for testing frameworks
- Use prompts C8-C9 for APIs and interfaces

## Best Practices

### 1. Preparation
- Have the IDR User Guide 2024Q4 SP1 PDF open and searchable
- Create a workspace for organizing responses
- Set up a development environment for testing generated code

### 2. Prompt Usage
- **Be Specific**: Always reference the exact User Guide version (2024Q4 SP1)
- **Request Examples**: Ask for concrete code examples and configuration samples
- **Seek References**: Request specific section numbers and page references
- **Validate Information**: Cross-reference responses with the actual User Guide

### 3. Progressive Development
```
Phase 1: Understanding (Prompts 1.1-2.2)
├── IDR Stream mode concepts
├── XML mapping requirements
└── System architecture design

Phase 2: Technical Design (Prompts T1-T10)
├── Detailed implementation planning
├── API and interface design
└── Performance and testing strategies

Phase 3: Code Generation (Prompts C1-C9)
├── Core component implementation
├── Utility and helper functions
└── Testing and validation code

Phase 4: Integration and Testing
├── System integration
├── Performance validation
└── Production deployment
```

## Prompt Customization

### Adding Context
When using any prompt, enhance it with:
```
Context: I'm working with IDR User Guide 2024Q4 SP1, specifically focusing on [specific section/feature].
Environment: [Your development environment, language preferences, etc.]
Constraints: [Any specific limitations or requirements]
```

### Requesting Specific Outputs
Modify prompts to request:
- **Code Examples**: "Please provide complete code examples with error handling"
- **Configuration Files**: "Include sample configuration files in IDR format"
- **API Documentation**: "Generate API documentation with request/response examples"
- **Test Cases**: "Provide comprehensive test cases with expected results"

## Common Prompt Patterns

### Pattern 1: Information Extraction
```
From the IDR User Guide 2024Q4 SP1, section [X], extract information about:
1. [Specific topic 1]
2. [Specific topic 2]
3. [Specific topic 3]

Please include:
- Exact quotes from the documentation
- Page/section references
- Code examples if available
- Configuration samples
```

### Pattern 2: Implementation Guidance
```
Based on the IDR User Guide 2024Q4 SP1, help me implement:
1. [Specific functionality]
2. [Integration requirements]
3. [Performance considerations]

Provide:
- Step-by-step implementation guide
- Code templates and examples
- Configuration requirements
- Testing strategies
```

### Pattern 3: Code Generation
```
Using the IDR User Guide 2024Q4 SP1 as reference, generate code for:
1. [Component description]
2. [Required functionality]
3. [Integration points]

Include:
- Complete implementation
- Error handling
- Documentation
- Unit tests
```

## Troubleshooting

### If Responses Are Too Generic
- Add more specific context about your use case
- Reference specific sections of the User Guide
- Request concrete examples and code samples
- Specify the exact IDR version (2024Q4 SP1)

### If Information Seems Outdated
- Always verify against the actual User Guide
- Cross-reference with multiple sections
- Ask for specific version compatibility notes
- Request migration guidance if needed

### If Code Doesn't Work
- Verify API compatibility with your IDR version
- Check configuration syntax against the User Guide
- Test components incrementally
- Review error messages against User Guide troubleshooting sections

## Next Steps After Using Prompts

1. **Document Findings**: Create a comprehensive specification document
2. **Create Project Structure**: Set up development environment and project structure
3. **Implement Incrementally**: Build and test components one at a time
4. **Validate Against Guide**: Regularly cross-reference with the User Guide
5. **Test Thoroughly**: Implement comprehensive testing as outlined in the prompts

## Support and Resources

- **Primary Reference**: IDR User Guide 2024Q4 SP1 (Documentation/IDR_User_Guide_2024Q4_SP1.pdf)
- **Prompt Files**: Use the three prompt files in sequence for comprehensive coverage
- **Development Approach**: Follow the progressive development phases outlined above

## Version Notes

These prompts are specifically designed for:
- **IDR Version**: 2024Q4 SP1
- **Focus**: IDR Stream mode XML processing
- **Scope**: Complete XML mapping generator implementation

Always verify compatibility with your specific IDR installation and requirements.
