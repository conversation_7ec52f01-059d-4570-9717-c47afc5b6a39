"""
Main IDR XML Mapping Generator
Based on IDR User Guide 2024Q4 SP1 Stream Mode
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import Optional

from core.schema_parser import XSDParser, SchemaAnalysis
from core.idr_types import (
    TransformDefinition, IDRConnection, ConnectionType, 
    GenerationConfig, CoreStreamType
)
from generators.dimension_builder import DimensionBuilder
from generators.fact_builder import FactBuilder
from generators.pipeline_generator import PipelineGenerator
from generators.xml_generator import XMLGenerator


def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('idr_generator.log')
        ]
    )


class IDRXMLMappingGenerator:
    """
    Main class for generating IDR XML mappings from XSD schemas
    """
    
    def __init__(self, config: Optional[GenerationConfig] = None):
        self.config = config or GenerationConfig()
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.xsd_parser = XSDParser()
        self.dimension_builder = DimensionBuilder(self.config)
        self.fact_builder = FactBuilder(self.config)
        self.pipeline_generator = PipelineGenerator(self.config)
        self.xml_generator = XMLGenerator()
    
    def generate_from_xsd(self, xsd_file_path: str, 
                         transform_name: str = "GeneratedTransform") -> str:
        """
        Generate IDR XML mapping from XSD file
        
        Args:
            xsd_file_path: Path to XSD file
            transform_name: Name for the transform definition
            
        Returns:
            Generated IDR XML as string
        """
        try:
            self.logger.info(f"Starting generation from XSD: {xsd_file_path}")
            
            # Parse XSD schema
            self.logger.info("Parsing XSD schema...")
            schema_analysis = self.xsd_parser.parse_schema(xsd_file_path)
            
            # Generate transform definition
            transform_def = self._create_transform_definition(
                schema_analysis, transform_name
            )
            
            # Generate XML
            self.logger.info("Generating IDR XML...")
            xml_output = self.xml_generator.generate_xml(transform_def)
            
            self.logger.info("Generation completed successfully")
            return xml_output
            
        except Exception as e:
            self.logger.error(f"Error generating IDR XML: {e}")
            raise
    
    def _create_transform_definition(self, schema_analysis: SchemaAnalysis, 
                                   transform_name: str) -> TransformDefinition:
        """Create complete transform definition from schema analysis"""
        
        # Create connections
        source_connections = [
            IDRConnection("S1", ConnectionType.IDR, is_source=True),
            IDRConnection("S2", ConnectionType.PRD, is_source=True)
        ]
        
        target_connections = [
            IDRConnection("T1", ConnectionType.IDR, is_source=False),
            IDRConnection("T2", ConnectionType.TEXT, is_source=False)
        ]
        
        # Build dimensions
        self.logger.info("Building dimensions...")
        dimensions = self.dimension_builder.build_dimensions(schema_analysis)
        
        # Add standard dimensions
        standard_dimensions = self.dimension_builder.create_standard_dimensions()
        dimensions.extend(standard_dimensions)
        
        # Validate dimensions
        dim_errors = self.dimension_builder.validate_dimensions(dimensions)
        if dim_errors:
            self.logger.warning(f"Dimension validation errors: {dim_errors}")
        
        # Build facts
        self.logger.info("Building facts...")
        facts = self.fact_builder.build_facts(schema_analysis, dimensions)
        
        # Add Prophet fact if no facts generated
        if not facts:
            prophet_fact = self.fact_builder.create_prophet_fact()
            facts.append(prophet_fact)
        
        # Optimize and validate facts
        facts = self.fact_builder.optimize_fact_structure(facts)
        fact_errors = self.fact_builder.validate_facts(facts)
        if fact_errors:
            self.logger.warning(f"Fact validation errors: {fact_errors}")
        
        # Generate pipelines
        self.logger.info("Generating pipelines...")
        pipelines = self.pipeline_generator.generate_pipelines(
            schema_analysis, facts, dimensions
        )
        
        # Create transform definition
        transform_def = TransformDefinition(
            name=transform_name,
            source_connections=source_connections,
            target_connections=target_connections,
            dimensions=dimensions,
            facts=facts,
            pipelines=pipelines
        )
        
        return transform_def
    
    def generate_components_separately(self, xsd_file_path: str) -> dict:
        """
        Generate IDR components separately for inspection
        
        Returns:
            Dictionary with separate XML components
        """
        try:
            # Parse schema
            schema_analysis = self.xsd_parser.parse_schema(xsd_file_path)
            
            # Build components
            dimensions = self.dimension_builder.build_dimensions(schema_analysis)
            dimensions.extend(self.dimension_builder.create_standard_dimensions())
            
            facts = self.fact_builder.build_facts(schema_analysis, dimensions)
            if not facts:
                facts.append(self.fact_builder.create_prophet_fact())
            
            pipelines = self.pipeline_generator.generate_pipelines(
                schema_analysis, facts, dimensions
            )
            
            # Generate separate XML components
            components = {
                'dimensions': self.xml_generator.generate_dimensions_xml(dimensions),
                'facts': self.xml_generator.generate_facts_xml(facts),
                'connections': self.xml_generator.generate_connection_xml([
                    IDRConnection("S1", ConnectionType.IDR, is_source=True),
                    IDRConnection("T1", ConnectionType.IDR, is_source=False)
                ])
            }
            
            return components
            
        except Exception as e:
            self.logger.error(f"Error generating components: {e}")
            raise
    
    def validate_generated_xml(self, xml_content: str) -> list:
        """
        Validate generated XML for common issues
        
        Returns:
            List of validation issues
        """
        issues = []
        
        try:
            import xml.etree.ElementTree as ET
            root = ET.fromstring(xml_content)
            
            # Check for required sections
            required_sections = ['Connections', 'Dimensions', 'Facts', 'Pipelines']
            for section in required_sections:
                if root.find(section) is None:
                    issues.append(f"Missing required section: {section}")
            
            # Check dimensions
            dimensions = root.find('Dimensions')
            if dimensions is not None:
                for dim in dimensions.findall('Dimension'):
                    name = dim.find('Name')
                    if name is None or not name.text:
                        issues.append("Dimension missing name")
                    
                    key = dim.find('Key')
                    if key is None or not key.get('type'):
                        issues.append("Dimension missing key type")
            
            # Check facts
            facts = root.find('Facts')
            if facts is not None:
                for fact in facts.findall('Fact'):
                    name = fact.find('Name')
                    if name is None or not name.text:
                        issues.append("Fact missing name")
                    
                    variables = fact.find('Variables')
                    if variables is None:
                        issues.append("Fact missing variables section")
            
            # Check pipelines
            pipelines = root.find('Pipelines')
            if pipelines is not None:
                for pipeline in pipelines.findall('Pipeline'):
                    name = pipeline.find('Name')
                    if name is None or not name.text:
                        issues.append("Pipeline missing name")
                    
                    initial_stage = pipeline.find('InitialStage')
                    if initial_stage is None:
                        issues.append("Pipeline missing initial stage")
            
        except ET.ParseError as e:
            issues.append(f"XML parsing error: {e}")
        except Exception as e:
            issues.append(f"Validation error: {e}")
        
        return issues


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Generate IDR XML mappings from XSD schemas"
    )
    
    parser.add_argument(
        "xsd_file", 
        help="Path to XSD file"
    )
    
    parser.add_argument(
        "-o", "--output",
        help="Output file path (default: stdout)"
    )
    
    parser.add_argument(
        "-n", "--name",
        default="GeneratedTransform",
        help="Transform definition name"
    )
    
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Logging level"
    )
    
    parser.add_argument(
        "--components",
        action="store_true",
        help="Generate components separately"
    )
    
    parser.add_argument(
        "--validate",
        action="store_true",
        help="Validate generated XML"
    )
    
    parser.add_argument(
        "--core-stream",
        choices=["PRJM", "PRJY", "STOM", "STOY", "IMP", "IDRM"],
        default="IMP",
        help="Default core stream type"
    )
    
    parser.add_argument(
        "--enable-merge",
        action="store_true",
        help="Enable merge operations in facts"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    try:
        # Validate input file
        xsd_path = Path(args.xsd_file)
        if not xsd_path.exists():
            logger.error(f"XSD file not found: {args.xsd_file}")
            sys.exit(1)
        
        # Create configuration
        config = GenerationConfig(
            default_core_stream=CoreStreamType(args.core_stream),
            enable_merge_operations=args.enable_merge,
            enable_validation=args.validate
        )
        
        # Create generator
        generator = IDRXMLMappingGenerator(config)
        
        if args.components:
            # Generate components separately
            logger.info("Generating components separately...")
            components = generator.generate_components_separately(str(xsd_path))
            
            for component_name, xml_content in components.items():
                output_file = f"{args.name}_{component_name}.xml"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(xml_content)
                logger.info(f"Generated {component_name}: {output_file}")
        
        else:
            # Generate complete XML
            xml_output = generator.generate_from_xsd(str(xsd_path), args.name)
            
            # Validate if requested
            if args.validate:
                issues = generator.validate_generated_xml(xml_output)
                if issues:
                    logger.warning("Validation issues found:")
                    for issue in issues:
                        logger.warning(f"  - {issue}")
                else:
                    logger.info("XML validation passed")
            
            # Output result
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(xml_output)
                logger.info(f"Generated IDR XML: {args.output}")
            else:
                print(xml_output)
        
    except Exception as e:
        logger.error(f"Generation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
