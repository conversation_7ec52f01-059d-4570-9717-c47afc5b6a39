"""
XML Generator for IDR XML Mapping Generator
Based on IDR User Guide 2024Q4 SP1 TransformDefinition structure
"""

import xml.etree.ElementTree as ET
from xml.dom import minidom
from typing import List, Dict, Optional
import logging
from core.idr_types import (
    TransformDefinition, IDRConnection, IDRDimension, IDRFact, IDRPipeline,
    TemporaryTable, PipelineStage, StreamVariable, Assignment, Filter, Target,
    AssignmentSourceType, CoreStreamType
)

logger = logging.getLogger(__name__)


class XMLGenerator:
    """
    Generates IDR Stream mode XML from TransformDefinition
    Based on User Guide structure from lines 4201-4220
    """
    
    def __init__(self):
        self.indent_size = 2
    
    def generate_xml(self, transform_def: TransformDefinition) -> str:
        """Generate complete IDR XML from TransformDefinition"""
        try:
            # Create root element
            root = ET.Element("TransformDefinition")
            
            # Add name
            name_elem = ET.SubElement(root, "Name")
            name_elem.text = transform_def.name
            
            # Add connections
            self._add_connections(root, transform_def)
            
            # Add dimensions
            self._add_dimensions(root, transform_def.dimensions)
            
            # Add facts
            self._add_facts(root, transform_def.facts)
            
            # Add temporary tables if any
            if transform_def.temporary_tables:
                self._add_temporary_tables(root, transform_def.temporary_tables)
            
            # Add source streams if any
            if transform_def.source_streams:
                self._add_source_streams(root, transform_def.source_streams)
            
            # Add pipelines
            self._add_pipelines(root, transform_def.pipelines)
            
            # Format and return XML
            return self._format_xml(root)
            
        except Exception as e:
            logger.error(f"Error generating XML: {e}")
            raise
    
    def _add_connections(self, root: ET.Element, transform_def: TransformDefinition):
        """Add connections section"""
        connections_elem = ET.SubElement(root, "Connections")
        
        # Source connections
        if transform_def.source_connections:
            source_conns = ET.SubElement(connections_elem, "SourceConnections")
            for conn in transform_def.source_connections:
                conn_elem = ET.SubElement(source_conns, "Connection")
                conn_elem.text = conn.name
        
        # Target connections
        if transform_def.target_connections:
            target_conns = ET.SubElement(connections_elem, "TargetConnections")
            for conn in transform_def.target_connections:
                conn_elem = ET.SubElement(target_conns, "Connection")
                conn_elem.text = conn.name
    
    def _add_dimensions(self, root: ET.Element, dimensions: List[IDRDimension]):
        """Add dimensions section"""
        if not dimensions:
            return
        
        dimensions_elem = ET.SubElement(root, "Dimensions")
        
        for dimension in dimensions:
            dim_elem = ET.SubElement(dimensions_elem, "Dimension")
            
            # Add attributes
            dim_elem.set("autoload", str(dimension.autoload).lower())
            dim_elem.set("dynamic", str(dimension.dynamic).lower())
            
            # Add child elements
            name_elem = ET.SubElement(dim_elem, "Name")
            name_elem.text = dimension.name
            
            table_elem = ET.SubElement(dim_elem, "TableName")
            table_elem.text = dimension.table_name
            
            conn_elem = ET.SubElement(dim_elem, "Connection")
            conn_elem.text = dimension.connection
            
            key_elem = ET.SubElement(dim_elem, "Key")
            key_elem.set("type", dimension.key_type)
            key_elem.text = dimension.key_column
            
            id_elem = ET.SubElement(dim_elem, "Id")
            id_elem.text = dimension.id_column
            
            # Add usable columns
            if dimension.usable_columns:
                usable_cols_elem = ET.SubElement(dim_elem, "UsableColumns")
                for column in dimension.usable_columns:
                    col_elem = ET.SubElement(usable_cols_elem, "UsableColumn")
                    col_elem.set("type", column.type)
                    col_elem.text = column.name
    
    def _add_facts(self, root: ET.Element, facts: List[IDRFact]):
        """Add facts section"""
        if not facts:
            return
        
        facts_elem = ET.SubElement(root, "Facts")
        
        for fact in facts:
            fact_elem = ET.SubElement(facts_elem, "Fact")
            
            # Add merge attribute if enabled
            if fact.merge_enabled:
                fact_elem.set("merge", "true")
            
            # Add child elements
            name_elem = ET.SubElement(fact_elem, "Name")
            name_elem.text = fact.name
            
            table_elem = ET.SubElement(fact_elem, "TableName")
            table_elem.text = fact.table_name
            
            conn_elem = ET.SubElement(fact_elem, "Connection")
            conn_elem.text = fact.connection
            
            # Add variables
            if fact.variables:
                vars_elem = ET.SubElement(fact_elem, "Variables")
                for variable in fact.variables:
                    var_elem = ET.SubElement(vars_elem, "Variable")
                    var_elem.set("type", variable.type)
                    if variable.is_match:
                        var_elem.set("match", "true")
                    var_elem.text = variable.name
    
    def _add_temporary_tables(self, root: ET.Element, temp_tables: List[TemporaryTable]):
        """Add temporary tables section"""
        temp_tables_elem = ET.SubElement(root, "TemporaryTables")
        
        for temp_table in temp_tables:
            table_elem = ET.SubElement(temp_tables_elem, "TemporaryTable")
            
            name_elem = ET.SubElement(table_elem, "Name")
            name_elem.text = temp_table.name
            
            key_elem = ET.SubElement(table_elem, "Key")
            key_elem.set("type", temp_table.key_type)
            key_elem.text = temp_table.key_column
            
            id_elem = ET.SubElement(table_elem, "Id")
            id_elem.set("type", temp_table.id_type)
            id_elem.text = temp_table.id_column
            
            # Add usable columns
            if temp_table.usable_columns:
                usable_cols_elem = ET.SubElement(table_elem, "UsableColumns")
                for column in temp_table.usable_columns:
                    col_elem = ET.SubElement(usable_cols_elem, "UsableColumn")
                    col_elem.set("type", column.type)
                    col_elem.text = column.name
    
    def _add_source_streams(self, root: ET.Element, source_streams: List[Dict]):
        """Add source streams section for IDR-to-IDR scenarios"""
        source_streams_elem = ET.SubElement(root, "SourceStreams")
        
        for stream in source_streams:
            stream_elem = ET.SubElement(source_streams_elem, "SourceStream")
            
            for key, value in stream.items():
                child_elem = ET.SubElement(stream_elem, key)
                child_elem.text = str(value)
    
    def _add_pipelines(self, root: ET.Element, pipelines: List[IDRPipeline]):
        """Add pipelines section"""
        if not pipelines:
            return
        
        pipelines_elem = ET.SubElement(root, "Pipelines")
        
        for pipeline in pipelines:
            pipeline_elem = ET.SubElement(pipelines_elem, "Pipeline")
            
            # Add validation attribute
            if pipeline.validation_enabled:
                pipeline_elem.set("validate", "true")
            
            # Add pipeline name
            name_elem = ET.SubElement(pipeline_elem, "Name")
            name_elem.text = pipeline.name
            
            # Add initial stage
            self._add_pipeline_stage(pipeline_elem, pipeline.initial_stage, is_initial=True)
            
            # Add additional stages
            if pipeline.additional_stages:
                stages_elem = ET.SubElement(pipeline_elem, "Stages")
                for stage in pipeline.additional_stages:
                    self._add_pipeline_stage(stages_elem, stage, is_initial=False)
    
    def _add_pipeline_stage(self, parent: ET.Element, stage: PipelineStage, is_initial: bool = False):
        """Add a pipeline stage"""
        stage_elem = ET.SubElement(parent, "InitialStage" if is_initial else "Stage")
        
        # Add stage name
        name_elem = ET.SubElement(stage_elem, "Name")
        name_elem.text = stage.name
        
        # Add core stream
        if stage.core_stream:
            core_stream_elem = ET.SubElement(stage_elem, "CoreStream")
            core_stream_elem.text = stage.core_stream.value
        
        # Add time range
        if stage.time_range:
            time_range_elem = ET.SubElement(stage_elem, "TimeRange")
            
            first_elem = ET.SubElement(time_range_elem, "First")
            first_elem.text = str(stage.time_range.first)
            
            times_elem = ET.SubElement(time_range_elem, "Times")
            times_elem.text = str(stage.time_range.times)
        
        # Add filters
        if stage.filters:
            filters_elem = ET.SubElement(stage_elem, "Filters")
            for filter_obj in stage.filters:
                filter_elem = ET.SubElement(filters_elem, filter_obj.filter_type)
                for value in filter_obj.values:
                    value_elem = ET.SubElement(filter_elem, "Value")
                    value_elem.text = value
        
        # Add key properties for non-initial stages
        if not is_initial and stage.key_properties:
            key_props_elem = ET.SubElement(stage_elem, "KeyProperties")
            for prop in stage.key_properties:
                prop_elem = ET.SubElement(key_props_elem, "KeyProperty")
                for key, value in prop.items():
                    prop_elem.set(key, value)
        
        # Add stream variables
        if stage.stream_variables:
            stream_vars_elem = ET.SubElement(stage_elem, "StreamVariables")
            for stream_var in stage.stream_variables:
                var_elem = ET.SubElement(stream_vars_elem, "StreamVariable")
                var_elem.set("type", stream_var.variable_type)
                if not stream_var.autoassign:
                    var_elem.set("autoassign", "false")
                if stream_var.aggregate_function:
                    var_elem.set("aggregate", stream_var.aggregate_function)
                if stream_var.index_range:
                    var_elem.set("index", stream_var.index_range)
                
                var_name_elem = ET.SubElement(var_elem, "VariableName")
                var_name_elem.text = stream_var.variable_name
                
                source_var_elem = ET.SubElement(var_elem, "SourceVariable")
                source_var_elem.text = stream_var.source_variable
        
        # Add assignments
        if stage.assignments:
            assignments_elem = ET.SubElement(stage_elem, "Assignments")
            for assignment in stage.assignments:
                assign_elem = ET.SubElement(assignments_elem, "Assignment")
                assign_elem.set("type", assignment.variable_type)
                
                var_name_elem = ET.SubElement(assign_elem, "VariableName")
                var_name_elem.text = assignment.variable_name
                
                # Add source based on type
                if assignment.source_type == AssignmentSourceType.KEY:
                    source_elem = ET.SubElement(assign_elem, "Key")
                    source_elem.text = assignment.source_value
                elif assignment.source_type == AssignmentSourceType.STREAM:
                    source_elem = ET.SubElement(assign_elem, "Stream")
                    source_elem.text = assignment.source_value
                elif assignment.source_type == AssignmentSourceType.EXPRESSION:
                    source_elem = ET.SubElement(assign_elem, "Expression")
                    # Use CDATA for complex expressions
                    source_elem.text = assignment.source_value
                elif assignment.source_type == AssignmentSourceType.GLOBAL:
                    source_elem = ET.SubElement(assign_elem, "Global")
                    source_elem.text = assignment.source_value
                
                # Add filter if present
                if assignment.filter_expression:
                    filter_elem = ET.SubElement(assign_elem, "Filter")
                    filter_elem.text = assignment.filter_expression
        
        # Add targets
        if stage.targets:
            targets_elem = ET.SubElement(stage_elem, "Targets")
            for target in stage.targets:
                target_elem = ET.SubElement(targets_elem, "Target")
                
                fact_elem = ET.SubElement(target_elem, "Fact")
                fact_elem.text = target.fact_name
                
                if target.filter_expression:
                    filter_elem = ET.SubElement(target_elem, "Filter")
                    filter_elem.text = target.filter_expression
                
                if target.skip_when_zero:
                    target_elem.set("skipWhenZero", "true")
    
    def _format_xml(self, root: ET.Element) -> str:
        """Format XML with proper indentation"""
        # Convert to string
        rough_string = ET.tostring(root, encoding='unicode')
        
        # Parse with minidom for pretty printing
        reparsed = minidom.parseString(rough_string)
        
        # Get pretty printed string
        pretty_xml = reparsed.toprettyxml(indent=' ' * self.indent_size)
        
        # Remove empty lines and fix formatting
        lines = [line for line in pretty_xml.split('\n') if line.strip()]
        
        # Remove XML declaration if present and add our own
        if lines[0].startswith('<?xml'):
            lines = lines[1:]
        
        # Add proper XML declaration
        formatted_lines = ['<?xml version="1.0" encoding="utf-8"?>'] + lines
        
        return '\n'.join(formatted_lines)
    
    def generate_connection_xml(self, connections: List[IDRConnection]) -> str:
        """Generate standalone connections XML"""
        root = ET.Element("Connections")
        
        source_conns = ET.SubElement(root, "SourceConnections")
        target_conns = ET.SubElement(root, "TargetConnections")
        
        for conn in connections:
            parent = source_conns if conn.is_source else target_conns
            conn_elem = ET.SubElement(parent, "Connection")
            conn_elem.text = conn.name
        
        return self._format_xml(root)
    
    def generate_dimensions_xml(self, dimensions: List[IDRDimension]) -> str:
        """Generate standalone dimensions XML"""
        root = ET.Element("Dimensions")
        self._add_dimensions(root, dimensions)
        return self._format_xml(root)
    
    def generate_facts_xml(self, facts: List[IDRFact]) -> str:
        """Generate standalone facts XML"""
        root = ET.Element("Facts")
        self._add_facts(root, facts)
        return self._format_xml(root)
