<?xml version="1.0" encoding="utf-8"?>
<TransformDefinition>
  <Name>ProphetTransform</Name>
  <Connections>
    <SourceConnections>
      <Connection>S1</Connection>
      <Connection>S2</Connection>
    </SourceConnections>
    <TargetConnections>
      <Connection>T1</Connection>
      <Connection>T2</Connection>
    </TargetConnections>
  </Connections>
  <Dimensions>
    <Dimension autoload="true" dynamic="true">
      <Name>DimProphetresultstype</Name>
      <TableName>DimProphetresultstype</TableName>
      <Connection>T1</Connection>
      <Key type="string">RunInfo</Key>
      <Id>ProphetResultsTypeId</Id>
      <UsableColumns>
        <UsableColumn type="string">ModelPoints</UsableColumn>
        <UsableColumn type="string">Results</UsableColumn>
        <UsableColumn type="string">version</UsableColumn>
      </UsableColumns>
    </Dimension>
    <Dimension autoload="true" dynamic="true">
      <Name>DimRuninfotype</Name>
      <TableName>DimRuninfotype</TableName>
      <Connection>T1</Connection>
      <Key type="string">RunDescription</Key>
      <Id>RunInfoTypeId</Id>
      <UsableColumns>
        <UsableColumn type="int">RunNumber</UsableColumn>
        <UsableColumn type="string">RunDate</UsableColumn>
        <UsableColumn type="int">SPCode</UsableColumn>
        <UsableColumn type="string">ProductCode</UsableColumn>
      </UsableColumns>
    </Dimension>
    <Dimension autoload="true" dynamic="true">
      <Name>DimModelpointstype</Name>
      <TableName>DimModelpointstype</TableName>
      <Connection>T1</Connection>
      <Key type="string">ModelPoint</Key>
      <Id>ModelPointsTypeId</Id>
    </Dimension>
    <Dimension autoload="false" dynamic="true">
      <Name>DimModelpointtype</Name>
      <TableName>DimModelpointtype</TableName>
      <Connection>T1</Connection>
      <Key type="string">PolicyNumber</Key>
      <Id>ModelPointTypeId</Id>
      <UsableColumns>
        <UsableColumn type="int">ProductId</UsableColumn>
        <UsableColumn type="int">IssueAge</UsableColumn>
        <UsableColumn type="string">Gender</UsableColumn>
        <UsableColumn type="double">SumAssured</UsableColumn>
        <UsableColumn type="string">PremiumFrequency</UsableColumn>
        <UsableColumn type="string">PolicyStatus</UsableColumn>
        <UsableColumn type="int">modelPointId</UsableColumn>
      </UsableColumns>
    </Dimension>
    <Dimension autoload="true" dynamic="true">
      <Name>DimResultstype</Name>
      <TableName>DimResultstype</TableName>
      <Connection>T1</Connection>
      <Key type="string">TimeSeriesResults</Key>
      <Id>ResultsTypeId</Id>
      <UsableColumns>
        <UsableColumn type="string">SummaryResults</UsableColumn>
      </UsableColumns>
    </Dimension>
    <Dimension autoload="true" dynamic="true">
      <Name>DimTimeseriesresultstype</Name>
      <TableName>DimTimeseriesresultstype</TableName>
      <Connection>T1</Connection>
      <Key type="string">TimePoint</Key>
      <Id>TimeSeriesResultsTypeId</Id>
    </Dimension>
    <Dimension autoload="false" dynamic="true">
      <Name>DimTimepointtype</Name>
      <TableName>DimTimepointtype</TableName>
      <Connection>T1</Connection>
      <Key type="int">MonthId</Key>
      <Id>TimePointTypeId</Id>
      <UsableColumns>
        <UsableColumn type="double">DEATH_OUTGO</UsableColumn>
        <UsableColumn type="double">SURR_OUTGO</UsableColumn>
        <UsableColumn type="double">ANNUAL_PREM</UsableColumn>
        <UsableColumn type="double">RESERVES</UsableColumn>
        <UsableColumn type="double">CASH_FLOW</UsableColumn>
        <UsableColumn type="double">PRESENT_VALUE</UsableColumn>
        <UsableColumn type="double">NEW_BUSINESS</UsableColumn>
        <UsableColumn type="int">POLICY_COUNT</UsableColumn>
        <UsableColumn type="double">LAPSE_RATE</UsableColumn>
        <UsableColumn type="double">MORTALITY_RATE</UsableColumn>
        <UsableColumn type="int">timeId</UsableColumn>
      </UsableColumns>
    </Dimension>
    <Dimension autoload="true" dynamic="true">
      <Name>DimSummaryresultstype</Name>
      <TableName>DimSummaryresultstype</TableName>
      <Connection>T1</Connection>
      <Key type="double">TotalPremium</Key>
      <Id>SummaryResultsTypeId</Id>
      <UsableColumns>
        <UsableColumn type="double">TotalBenefits</UsableColumn>
        <UsableColumn type="double">NetPresentValue</UsableColumn>
        <UsableColumn type="double">InternalRateOfReturn</UsableColumn>
        <UsableColumn type="double">Profitability</UsableColumn>
      </UsableColumns>
    </Dimension>
    <Dimension autoload="false" dynamic="true">
      <Name>DimRegioncode</Name>
      <TableName>DimRegioncode</TableName>
      <Connection>T1</Connection>
      <Key type="string">RegionCode</Key>
      <Id>RegionCodeId</Id>
    </Dimension>
    <Dimension autoload="false" dynamic="true">
      <Name>DimAgentcode</Name>
      <TableName>DimAgentcode</TableName>
      <Connection>T1</Connection>
      <Key type="string">AgentCode</Key>
      <Id>AgentCodeId</Id>
    </Dimension>
    <Dimension autoload="false" dynamic="true">
      <Name>DimChannelcode</Name>
      <TableName>DimChannelcode</TableName>
      <Connection>T1</Connection>
      <Key type="string">ChannelCode</Key>
      <Id>ChannelCodeId</Id>
    </Dimension>
    <Dimension autoload="true" dynamic="false">
      <Name>DimTime</Name>
      <TableName>DimTime</TableName>
      <Connection>T1</Connection>
      <Key type="int">MonthId</Key>
      <Id>TimeId</Id>
      <UsableColumns>
        <UsableColumn type="int">Year</UsableColumn>
        <UsableColumn type="int">Month</UsableColumn>
        <UsableColumn type="int">Quarter</UsableColumn>
        <UsableColumn type="string">MonthName</UsableColumn>
      </UsableColumns>
    </Dimension>
    <Dimension autoload="true" dynamic="true">
      <Name>DimRun</Name>
      <TableName>DimRun</TableName>
      <Connection>T1</Connection>
      <Key type="int">RunNumber</Key>
      <Id>RunId</Id>
      <UsableColumns>
        <UsableColumn type="string">RunName</UsableColumn>
        <UsableColumn type="string">RunDate</UsableColumn>
        <UsableColumn type="string">RunType</UsableColumn>
      </UsableColumns>
    </Dimension>
    <Dimension autoload="true" dynamic="true">
      <Name>DimSPCode</Name>
      <TableName>DimSPCode</TableName>
      <Connection>T1</Connection>
      <Key type="int">SPCode</Key>
      <Id>SPCodeId</Id>
      <UsableColumns>
        <UsableColumn type="string">SPCodeName</UsableColumn>
        <UsableColumn type="string">SPCodeType</UsableColumn>
      </UsableColumns>
    </Dimension>
  </Dimensions>
  <Facts>
    <Fact>
      <Name>FactProphetresultstype</Name>
      <TableName>FactProphetresultstype</TableName>
      <Connection>T1</Connection>
      <Variables>
        <Variable type="int">JobId</Variable>
        <Variable type="int">RunNumber</Variable>
        <Variable type="int">SPCode</Variable>
        <Variable type="int">MonthId</Variable>
        <Variable type="int">ProphetResultsTypeId</Variable>
        <Variable type="int">RunInfoTypeId</Variable>
        <Variable type="int">ModelPointsTypeId</Variable>
        <Variable type="int">ModelPointTypeId</Variable>
        <Variable type="int">ResultsTypeId</Variable>
        <Variable type="int">TimeSeriesResultsTypeId</Variable>
        <Variable type="int">TimePointTypeId</Variable>
        <Variable type="int">SummaryResultsTypeId</Variable>
        <Variable type="int">RegionCodeId</Variable>
        <Variable type="int">AgentCodeId</Variable>
        <Variable type="int">ChannelCodeId</Variable>
        <Variable type="int">TimeId</Variable>
        <Variable type="int">RunId</Variable>
        <Variable type="int">SPCodeId</Variable>
        <Variable type="string">RunInfo</Variable>
        <Variable type="string">ModelPoints</Variable>
        <Variable type="string">Results</Variable>
        <Variable type="string">version</Variable>
      </Variables>
    </Fact>
    <Fact>
      <Name>FactRuninfotype</Name>
      <TableName>FactRuninfotype</TableName>
      <Connection>T1</Connection>
      <Variables>
        <Variable type="int">JobId</Variable>
        <Variable type="int">RunNumber</Variable>
        <Variable type="int">SPCode</Variable>
        <Variable type="int">MonthId</Variable>
        <Variable type="int">ProphetResultsTypeId</Variable>
        <Variable type="int">RunInfoTypeId</Variable>
        <Variable type="int">ModelPointsTypeId</Variable>
        <Variable type="int">ModelPointTypeId</Variable>
        <Variable type="int">ResultsTypeId</Variable>
        <Variable type="int">TimeSeriesResultsTypeId</Variable>
        <Variable type="int">TimePointTypeId</Variable>
        <Variable type="int">SummaryResultsTypeId</Variable>
        <Variable type="int">RegionCodeId</Variable>
        <Variable type="int">AgentCodeId</Variable>
        <Variable type="int">ChannelCodeId</Variable>
        <Variable type="int">TimeId</Variable>
        <Variable type="int">RunId</Variable>
        <Variable type="int">SPCodeId</Variable>
        <Variable type="string">RunDate</Variable>
        <Variable type="string">RunDescription</Variable>
        <Variable type="string">ProductCode</Variable>
      </Variables>
    </Fact>
    <Fact>
      <Name>FactModelpointstype</Name>
      <TableName>FactModelpointstype</TableName>
      <Connection>T1</Connection>
      <Variables>
        <Variable type="int">JobId</Variable>
        <Variable type="int">RunNumber</Variable>
        <Variable type="int">SPCode</Variable>
        <Variable type="int">MonthId</Variable>
        <Variable type="int">ProphetResultsTypeId</Variable>
        <Variable type="int">RunInfoTypeId</Variable>
        <Variable type="int">ModelPointsTypeId</Variable>
        <Variable type="int">ModelPointTypeId</Variable>
        <Variable type="int">ResultsTypeId</Variable>
        <Variable type="int">TimeSeriesResultsTypeId</Variable>
        <Variable type="int">TimePointTypeId</Variable>
        <Variable type="int">SummaryResultsTypeId</Variable>
        <Variable type="int">RegionCodeId</Variable>
        <Variable type="int">AgentCodeId</Variable>
        <Variable type="int">ChannelCodeId</Variable>
        <Variable type="int">TimeId</Variable>
        <Variable type="int">RunId</Variable>
        <Variable type="int">SPCodeId</Variable>
        <Variable type="string">ModelPoint</Variable>
      </Variables>
    </Fact>
    <Fact>
      <Name>FactModelpointtype</Name>
      <TableName>FactModelpointtype</TableName>
      <Connection>T1</Connection>
      <Variables>
        <Variable type="int">JobId</Variable>
        <Variable type="int">RunNumber</Variable>
        <Variable type="int">SPCode</Variable>
        <Variable type="int">MonthId</Variable>
        <Variable type="int">ProphetResultsTypeId</Variable>
        <Variable type="int">RunInfoTypeId</Variable>
        <Variable type="int">ModelPointsTypeId</Variable>
        <Variable type="int">ModelPointTypeId</Variable>
        <Variable type="int">ResultsTypeId</Variable>
        <Variable type="int">TimeSeriesResultsTypeId</Variable>
        <Variable type="int">TimePointTypeId</Variable>
        <Variable type="int">SummaryResultsTypeId</Variable>
        <Variable type="int">RegionCodeId</Variable>
        <Variable type="int">AgentCodeId</Variable>
        <Variable type="int">ChannelCodeId</Variable>
        <Variable type="int">TimeId</Variable>
        <Variable type="int">RunId</Variable>
        <Variable type="int">SPCodeId</Variable>
        <Variable type="string">PolicyNumber</Variable>
        <Variable type="int">ProductId</Variable>
        <Variable type="int">IssueAge</Variable>
        <Variable type="string">Gender</Variable>
        <Variable type="double">SumAssured</Variable>
        <Variable type="string">PremiumFrequency</Variable>
        <Variable type="string">PolicyStatus</Variable>
        <Variable type="int">modelPointId</Variable>
      </Variables>
    </Fact>
    <Fact>
      <Name>FactResultstype</Name>
      <TableName>FactResultstype</TableName>
      <Connection>T1</Connection>
      <Variables>
        <Variable type="int">JobId</Variable>
        <Variable type="int">RunNumber</Variable>
        <Variable type="int">SPCode</Variable>
        <Variable type="int">MonthId</Variable>
        <Variable type="int">ProphetResultsTypeId</Variable>
        <Variable type="int">RunInfoTypeId</Variable>
        <Variable type="int">ModelPointsTypeId</Variable>
        <Variable type="int">ModelPointTypeId</Variable>
        <Variable type="int">ResultsTypeId</Variable>
        <Variable type="int">TimeSeriesResultsTypeId</Variable>
        <Variable type="int">TimePointTypeId</Variable>
        <Variable type="int">SummaryResultsTypeId</Variable>
        <Variable type="int">RegionCodeId</Variable>
        <Variable type="int">AgentCodeId</Variable>
        <Variable type="int">ChannelCodeId</Variable>
        <Variable type="int">TimeId</Variable>
        <Variable type="int">RunId</Variable>
        <Variable type="int">SPCodeId</Variable>
        <Variable type="string">TimeSeriesResults</Variable>
        <Variable type="string">SummaryResults</Variable>
      </Variables>
    </Fact>
    <Fact>
      <Name>FactTimeseriesresultstype</Name>
      <TableName>FactTimeseriesresultstype</TableName>
      <Connection>T1</Connection>
      <Variables>
        <Variable type="int">JobId</Variable>
        <Variable type="int">RunNumber</Variable>
        <Variable type="int">SPCode</Variable>
        <Variable type="int">MonthId</Variable>
        <Variable type="int">ProphetResultsTypeId</Variable>
        <Variable type="int">RunInfoTypeId</Variable>
        <Variable type="int">ModelPointsTypeId</Variable>
        <Variable type="int">ModelPointTypeId</Variable>
        <Variable type="int">ResultsTypeId</Variable>
        <Variable type="int">TimeSeriesResultsTypeId</Variable>
        <Variable type="int">TimePointTypeId</Variable>
        <Variable type="int">SummaryResultsTypeId</Variable>
        <Variable type="int">RegionCodeId</Variable>
        <Variable type="int">AgentCodeId</Variable>
        <Variable type="int">ChannelCodeId</Variable>
        <Variable type="int">TimeId</Variable>
        <Variable type="int">RunId</Variable>
        <Variable type="int">SPCodeId</Variable>
        <Variable type="string">TimePoint</Variable>
      </Variables>
    </Fact>
    <Fact>
      <Name>FactTimepointtype</Name>
      <TableName>FactTimepointtype</TableName>
      <Connection>T1</Connection>
      <Variables>
        <Variable type="int">JobId</Variable>
        <Variable type="int">RunNumber</Variable>
        <Variable type="int">SPCode</Variable>
        <Variable type="int">MonthId</Variable>
        <Variable type="int">ProphetResultsTypeId</Variable>
        <Variable type="int">RunInfoTypeId</Variable>
        <Variable type="int">ModelPointsTypeId</Variable>
        <Variable type="int">ModelPointTypeId</Variable>
        <Variable type="int">ResultsTypeId</Variable>
        <Variable type="int">TimeSeriesResultsTypeId</Variable>
        <Variable type="int">TimePointTypeId</Variable>
        <Variable type="int">SummaryResultsTypeId</Variable>
        <Variable type="int">RegionCodeId</Variable>
        <Variable type="int">AgentCodeId</Variable>
        <Variable type="int">ChannelCodeId</Variable>
        <Variable type="int">TimeId</Variable>
        <Variable type="int">RunId</Variable>
        <Variable type="int">SPCodeId</Variable>
        <Variable type="double">DEATH_OUTGO</Variable>
        <Variable type="double">SURR_OUTGO</Variable>
        <Variable type="double">ANNUAL_PREM</Variable>
        <Variable type="double">RESERVES</Variable>
        <Variable type="double">CASH_FLOW</Variable>
        <Variable type="double">PRESENT_VALUE</Variable>
        <Variable type="double">NEW_BUSINESS</Variable>
        <Variable type="int">POLICY_COUNT</Variable>
        <Variable type="double">LAPSE_RATE</Variable>
        <Variable type="double">MORTALITY_RATE</Variable>
        <Variable type="int">timeId</Variable>
      </Variables>
    </Fact>
    <Fact>
      <Name>FactSummaryresultstype</Name>
      <TableName>FactSummaryresultstype</TableName>
      <Connection>T1</Connection>
      <Variables>
        <Variable type="int">JobId</Variable>
        <Variable type="int">RunNumber</Variable>
        <Variable type="int">SPCode</Variable>
        <Variable type="int">MonthId</Variable>
        <Variable type="int">ProphetResultsTypeId</Variable>
        <Variable type="int">RunInfoTypeId</Variable>
        <Variable type="int">ModelPointsTypeId</Variable>
        <Variable type="int">ModelPointTypeId</Variable>
        <Variable type="int">ResultsTypeId</Variable>
        <Variable type="int">TimeSeriesResultsTypeId</Variable>
        <Variable type="int">TimePointTypeId</Variable>
        <Variable type="int">SummaryResultsTypeId</Variable>
        <Variable type="int">RegionCodeId</Variable>
        <Variable type="int">AgentCodeId</Variable>
        <Variable type="int">ChannelCodeId</Variable>
        <Variable type="int">TimeId</Variable>
        <Variable type="int">RunId</Variable>
        <Variable type="int">SPCodeId</Variable>
        <Variable type="double">TotalPremium</Variable>
        <Variable type="double">TotalBenefits</Variable>
        <Variable type="double">NetPresentValue</Variable>
        <Variable type="double">InternalRateOfReturn</Variable>
        <Variable type="double">Profitability</Variable>
      </Variables>
    </Fact>
    <Fact>
      <Name>FactPremiumamount</Name>
      <TableName>FactPremiumamount</TableName>
      <Connection>T1</Connection>
      <Variables>
        <Variable type="int">JobId</Variable>
        <Variable type="int">RunNumber</Variable>
        <Variable type="int">SPCode</Variable>
        <Variable type="int">MonthId</Variable>
        <Variable type="int">ProphetResultsTypeId</Variable>
        <Variable type="int">RunInfoTypeId</Variable>
        <Variable type="int">ModelPointsTypeId</Variable>
        <Variable type="int">ModelPointTypeId</Variable>
        <Variable type="int">ResultsTypeId</Variable>
        <Variable type="int">TimeSeriesResultsTypeId</Variable>
        <Variable type="int">TimePointTypeId</Variable>
        <Variable type="int">SummaryResultsTypeId</Variable>
        <Variable type="int">RegionCodeId</Variable>
        <Variable type="int">AgentCodeId</Variable>
        <Variable type="int">ChannelCodeId</Variable>
        <Variable type="int">TimeId</Variable>
        <Variable type="int">RunId</Variable>
        <Variable type="int">SPCodeId</Variable>
        <Variable type="string">PremiumAmount</Variable>
      </Variables>
    </Fact>
  </Facts>
  <Pipelines>
    <Pipeline validate="true">
      <Name>MainDataPipeline</Name>
      <InitialStage>
        <Name>InitialStage</Name>
        <CoreStream>IMP</CoreStream>
        <Filters>
          <RunNumbers>
            <Value>1</Value>
          </RunNumbers>
          <Connections>
            <Value>S1</Value>
          </Connections>
        </Filters>
        <StreamVariables>
          <StreamVariable type="string">
            <VariableName>RunInfo</VariableName>
            <SourceVariable>RunInfo</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>ModelPoints</VariableName>
            <SourceVariable>ModelPoints</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>Results</VariableName>
            <SourceVariable>Results</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>version</VariableName>
            <SourceVariable>version</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>RunDate</VariableName>
            <SourceVariable>RunDate</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>RunDescription</VariableName>
            <SourceVariable>RunDescription</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>ProductCode</VariableName>
            <SourceVariable>ProductCode</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>ModelPoint</VariableName>
            <SourceVariable>ModelPoint</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>PolicyNumber</VariableName>
            <SourceVariable>PolicyNumber</SourceVariable>
          </StreamVariable>
          <StreamVariable type="int">
            <VariableName>IssueAge</VariableName>
            <SourceVariable>IssueAge</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>Gender</VariableName>
            <SourceVariable>Gender</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>SumAssured</VariableName>
            <SourceVariable>SumAssured</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>PremiumFrequency</VariableName>
            <SourceVariable>PremiumFrequency</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>PolicyStatus</VariableName>
            <SourceVariable>PolicyStatus</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>TimeSeriesResults</VariableName>
            <SourceVariable>TimeSeriesResults</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>SummaryResults</VariableName>
            <SourceVariable>SummaryResults</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>TimePoint</VariableName>
            <SourceVariable>TimePoint</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>DEATH_OUTGO</VariableName>
            <SourceVariable>DEATH_OUTGO</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>SURR_OUTGO</VariableName>
            <SourceVariable>SURR_OUTGO</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>ANNUAL_PREM</VariableName>
            <SourceVariable>ANNUAL_PREM</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>RESERVES</VariableName>
            <SourceVariable>RESERVES</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>CASH_FLOW</VariableName>
            <SourceVariable>CASH_FLOW</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>PRESENT_VALUE</VariableName>
            <SourceVariable>PRESENT_VALUE</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>NEW_BUSINESS</VariableName>
            <SourceVariable>NEW_BUSINESS</SourceVariable>
          </StreamVariable>
          <StreamVariable type="int">
            <VariableName>POLICY_COUNT</VariableName>
            <SourceVariable>POLICY_COUNT</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>LAPSE_RATE</VariableName>
            <SourceVariable>LAPSE_RATE</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>MORTALITY_RATE</VariableName>
            <SourceVariable>MORTALITY_RATE</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>TotalPremium</VariableName>
            <SourceVariable>TotalPremium</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>TotalBenefits</VariableName>
            <SourceVariable>TotalBenefits</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>NetPresentValue</VariableName>
            <SourceVariable>NetPresentValue</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>InternalRateOfReturn</VariableName>
            <SourceVariable>InternalRateOfReturn</SourceVariable>
          </StreamVariable>
          <StreamVariable type="double">
            <VariableName>Profitability</VariableName>
            <SourceVariable>Profitability</SourceVariable>
          </StreamVariable>
          <StreamVariable type="string">
            <VariableName>PremiumAmount</VariableName>
            <SourceVariable>PremiumAmount</SourceVariable>
          </StreamVariable>
        </StreamVariables>
        <Assignments>
          <Assignment type="int">
            <VariableName>JobId</VariableName>
            <Key>JobId</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>RunNumber</VariableName>
            <Key>RunNumber</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>SPCode</VariableName>
            <Key>SPCode</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>MonthId</VariableName>
            <Key>Time</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>ProphetResultsTypeId</VariableName>
            <Key>RunInfo</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>RunInfoTypeId</VariableName>
            <Key>RunDescription</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>ModelPointsTypeId</VariableName>
            <Key>ModelPoint</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>ModelPointTypeId</VariableName>
            <Key>PolicyNumber</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>ResultsTypeId</VariableName>
            <Key>TimeSeriesResults</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>TimeSeriesResultsTypeId</VariableName>
            <Key>TimePoint</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>TimePointTypeId</VariableName>
            <Key>MonthId</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>SummaryResultsTypeId</VariableName>
            <Key>TotalPremium</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>RegionCodeId</VariableName>
            <Key>RegionCode</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>AgentCodeId</VariableName>
            <Key>AgentCode</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>ChannelCodeId</VariableName>
            <Key>ChannelCode</Key>
          </Assignment>
        </Assignments>
        <Targets>
          <Target>
            <Fact>FactProphetresultstype</Fact>
          </Target>
          <Target>
            <Fact>FactRuninfotype</Fact>
          </Target>
          <Target>
            <Fact>FactModelpointstype</Fact>
          </Target>
          <Target>
            <Fact>FactModelpointtype</Fact>
          </Target>
          <Target>
            <Fact>FactResultstype</Fact>
          </Target>
          <Target>
            <Fact>FactTimeseriesresultstype</Fact>
          </Target>
          <Target>
            <Fact>FactTimepointtype</Fact>
          </Target>
          <Target>
            <Fact>FactSummaryresultstype</Fact>
          </Target>
          <Target>
            <Fact>FactPremiumamount</Fact>
          </Target>
        </Targets>
      </InitialStage>
    </Pipeline>
    <Pipeline validate="true">
      <Name>ValidationPipeline</Name>
      <InitialStage>
        <Name>ValidationStage</Name>
        <CoreStream>IMP</CoreStream>
        <TimeRange>
          <First>0</First>
          <Times>1</Times>
        </TimeRange>
        <Filters>
          <RunNumbers>
            <Value>1</Value>
          </RunNumbers>
        </Filters>
        <StreamVariables>
          <StreamVariable type="string" autoassign="false">
            <VariableName>PolicyNumber</VariableName>
            <SourceVariable>Asset_Name</SourceVariable>
          </StreamVariable>
        </StreamVariables>
        <Assignments>
          <Assignment type="string">
            <VariableName>Key</VariableName>
            <Expression>&quot;Validation: Data Quality Check&quot;</Expression>
          </Assignment>
          <Assignment type="string">
            <VariableName>Level</VariableName>
            <Global>INFO</Global>
          </Assignment>
          <Assignment type="string">
            <VariableName>Message</VariableName>
            <Expression>&quot;Pipeline validation completed successfully&quot;</Expression>
          </Assignment>
        </Assignments>
        <Targets>
          <Target>
            <Fact>~~Validation~~</Fact>
          </Target>
        </Targets>
      </InitialStage>
    </Pipeline>
  </Pipelines>
</TransformDefinition>