"""
Dimension Builder for IDR XML Mapping Generator
Based on IDR User Guide 2024Q4 SP1 lines 4246-4270
"""

from typing import List, Dict, Optional
import logging
from core.schema_parser import SchemaAnalysis, XSDElement, XSDComplexType, TypeMapper
from core.idr_types import (
    IDRDimension, UsableColumn, IDRNamingConventions,
    GenerationConfig
)

logger = logging.getLogger(__name__)


class DimensionBuilder:
    """
    Builds IDR Dimension definitions from XSD schema analysis
    Based on User Guide examples from lines 4246-4270
    """
    
    def __init__(self, config: GenerationConfig):
        self.config = config
        self.type_mapper = TypeMapper()
        self.naming = IDRNamingConventions()
    
    def build_dimensions(self, schema_analysis: SchemaAnalysis) -> List[IDRDimension]:
        """Build dimensions from schema analysis"""
        dimensions = []
        
        if not self.config.auto_create_dimensions:
            return dimensions
        
        # Create dimensions from complex types
        for complex_type in schema_analysis.complex_types:
            dimension = self._create_dimension_from_complex_type(complex_type)
            if dimension:
                dimensions.append(dimension)
        
        # Create dimensions from root elements that could be lookup tables
        for element in schema_analysis.root_elements:
            if self._should_create_dimension_for_element(element):
                dimension = self._create_dimension_from_element(element, schema_analysis)
                if dimension:
                    dimensions.append(dimension)
        
        logger.info(f"Created {len(dimensions)} dimensions from schema")
        return dimensions
    
    def _create_dimension_from_complex_type(self, complex_type: XSDComplexType) -> Optional[IDRDimension]:
        """Create dimension from XSD complex type"""
        try:
            # Generate names using naming conventions
            dimension_name = self.naming.generate_dimension_name(complex_type.name)
            table_name = self.naming.generate_table_name(complex_type.name, is_dimension=True)
            
            # Find key column (first string element or first element)
            key_element = self._find_key_element(complex_type.elements)
            if not key_element:
                logger.warning(f"No suitable key element found for complex type {complex_type.name}")
                return None
            
            key_type = self.type_mapper.map_xsd_to_idr(key_element.type)
            
            # Create usable columns from other elements and attributes
            usable_columns = self._create_usable_columns(complex_type.elements, key_element)
            usable_columns.extend(self._create_usable_columns(complex_type.attributes, key_element))
            
            return IDRDimension(
                name=dimension_name,
                table_name=table_name,
                connection="T1",  # Default target connection
                key_column=self.naming.sanitize_name(key_element.name),
                key_type=key_type,
                id_column=f"{self.naming.sanitize_name(complex_type.name)}Id",
                usable_columns=usable_columns,
                autoload=self._should_use_autoload(complex_type),
                dynamic=self._should_use_dynamic(complex_type)
            )
            
        except Exception as e:
            logger.error(f"Error creating dimension from complex type {complex_type.name}: {e}")
            return None
    
    def _create_dimension_from_element(self, element: XSDElement, 
                                     schema_analysis: SchemaAnalysis) -> Optional[IDRDimension]:
        """Create dimension from root element"""
        try:
            # Generate names
            dimension_name = self.naming.generate_dimension_name(element.name)
            table_name = self.naming.generate_table_name(element.name, is_dimension=True)
            
            # Use element itself as key
            key_type = self.type_mapper.map_xsd_to_idr(element.type)
            
            # Create minimal dimension with just key
            return IDRDimension(
                name=dimension_name,
                table_name=table_name,
                connection="T1",
                key_column=self.naming.sanitize_name(element.name),
                key_type=key_type,
                id_column=f"{self.naming.sanitize_name(element.name)}Id",
                usable_columns=[],
                autoload=False,
                dynamic=True  # Allow dynamic creation for simple lookups
            )
            
        except Exception as e:
            logger.error(f"Error creating dimension from element {element.name}: {e}")
            return None
    
    def _find_key_element(self, elements: List[XSDElement]) -> Optional[XSDElement]:
        """Find the best element to use as dimension key"""
        if not elements:
            return None
        
        # Prefer string elements for keys
        string_elements = [e for e in elements if 'string' in e.type.lower()]
        if string_elements:
            return string_elements[0]
        
        # Prefer elements with 'id', 'name', 'code' in name
        key_candidates = [e for e in elements 
                         if any(keyword in e.name.lower() 
                               for keyword in ['id', 'name', 'code', 'key'])]
        if key_candidates:
            return key_candidates[0]
        
        # Fall back to first element
        return elements[0]
    
    def _create_usable_columns(self, elements: List[XSDElement], 
                             key_element: XSDElement) -> List[UsableColumn]:
        """Create usable columns from elements, excluding the key element"""
        usable_columns = []
        
        for element in elements:
            if element.name == key_element.name:
                continue  # Skip key element
            
            try:
                idr_type = self.type_mapper.map_xsd_to_idr(element.type)
                column_name = self.naming.sanitize_name(element.name)
                
                usable_column = UsableColumn(
                    name=column_name,
                    type=idr_type,
                    description=element.documentation
                )
                usable_columns.append(usable_column)
                
            except Exception as e:
                logger.warning(f"Error creating usable column for {element.name}: {e}")
        
        return usable_columns
    
    def _should_create_dimension_for_element(self, element: XSDElement) -> bool:
        """Determine if element should become a dimension"""
        # Create dimensions for elements that look like lookup values
        lookup_indicators = ['id', 'code', 'type', 'category', 'status', 'region']
        element_name_lower = element.name.lower()
        
        return any(indicator in element_name_lower for indicator in lookup_indicators)
    
    def _should_use_autoload(self, complex_type: XSDComplexType) -> bool:
        """Determine if dimension should use autoload"""
        # Use autoload for small lookup tables (heuristic: few elements)
        return len(complex_type.elements) <= 5
    
    def _should_use_dynamic(self, complex_type: XSDComplexType) -> bool:
        """Determine if dimension should be dynamic"""
        # Use dynamic for dimensions that might have new values
        dynamic_indicators = ['code', 'type', 'category']
        type_name_lower = complex_type.name.lower()
        
        return any(indicator in type_name_lower for indicator in dynamic_indicators)
    
    def create_standard_dimensions(self) -> List[IDRDimension]:
        """Create standard IDR dimensions that are commonly needed"""
        standard_dimensions = []
        
        # Time dimension
        time_dimension = IDRDimension(
            name="DimTime",
            table_name="DimTime",
            connection="T1",
            key_column="MonthId",
            key_type="int",
            id_column="TimeId",
            usable_columns=[
                UsableColumn("Year", "int", "Calendar year"),
                UsableColumn("Month", "int", "Calendar month"),
                UsableColumn("Quarter", "int", "Calendar quarter"),
                UsableColumn("MonthName", "string", "Month name")
            ],
            autoload=True,
            dynamic=False
        )
        standard_dimensions.append(time_dimension)
        
        # Run dimension
        run_dimension = IDRDimension(
            name="DimRun",
            table_name="DimRun",
            connection="T1",
            key_column="RunNumber",
            key_type="int",
            id_column="RunId",
            usable_columns=[
                UsableColumn("RunName", "string", "Run description"),
                UsableColumn("RunDate", "string", "Run execution date"),
                UsableColumn("RunType", "string", "Type of run")
            ],
            autoload=True,
            dynamic=True
        )
        standard_dimensions.append(run_dimension)
        
        # SP Code dimension
        sp_dimension = IDRDimension(
            name="DimSPCode",
            table_name="DimSPCode",
            connection="T1",
            key_column="SPCode",
            key_type="int",
            id_column="SPCodeId",
            usable_columns=[
                UsableColumn("SPCodeName", "string", "SP Code description"),
                UsableColumn("SPCodeType", "string", "SP Code category")
            ],
            autoload=True,
            dynamic=True
        )
        standard_dimensions.append(sp_dimension)
        
        return standard_dimensions
    
    def validate_dimensions(self, dimensions: List[IDRDimension]) -> List[Dict[str, str]]:
        """Validate dimension definitions"""
        validation_errors = []
        
        dimension_names = set()
        table_names = set()
        
        for dimension in dimensions:
            # Check for duplicate names
            if dimension.name in dimension_names:
                validation_errors.append({
                    'type': 'duplicate_name',
                    'dimension': dimension.name,
                    'message': f'Duplicate dimension name: {dimension.name}'
                })
            dimension_names.add(dimension.name)
            
            # Check for duplicate table names
            if dimension.table_name in table_names:
                validation_errors.append({
                    'type': 'duplicate_table',
                    'dimension': dimension.name,
                    'message': f'Duplicate table name: {dimension.table_name}'
                })
            table_names.add(dimension.table_name)
            
            # Validate key column type
            if dimension.key_type not in ['string', 'int', 'double']:
                validation_errors.append({
                    'type': 'invalid_key_type',
                    'dimension': dimension.name,
                    'message': f'Invalid key type: {dimension.key_type}'
                })
            
            # Validate usable column types
            for column in dimension.usable_columns:
                if column.type not in ['string', 'int', 'double', 'bool']:
                    validation_errors.append({
                        'type': 'invalid_column_type',
                        'dimension': dimension.name,
                        'column': column.name,
                        'message': f'Invalid column type: {column.type}'
                    })
        
        return validation_errors
