# PowerShell script to convert PDF to text using .NET libraries
# This method preserves formatting better than simple copy/paste

param(
    [Parameter(Mandatory=$true)]
    [string]$PdfPath,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath = ""
)

# Set default output path if not provided
if ($OutputPath -eq "") {
    $OutputPath = [System.IO.Path]::ChangeExtension($PdfPath, ".txt")
}

try {
    # Load required assemblies
    Add-Type -AssemblyName System.Drawing
    Add-Type -AssemblyName System.Windows.Forms
    
    Write-Host "Converting PDF to text..."
    Write-Host "Input: $PdfPath"
    Write-Host "Output: $OutputPath"
    
    # Note: This is a basic approach. For better results, consider using dedicated PDF libraries
    # This script provides a framework - you may need to install additional libraries
    
    # Alternative approach using COM object (if available)
    try {
        $word = New-Object -ComObject Word.Application
        $word.Visible = $false
        $doc = $word.Documents.Open($PdfPath)
        $doc.SaveAs2($OutputPath, 2) # 2 = wdFormatText
        $doc.Close()
        $word.Quit()
        Write-Host "Conversion completed successfully using Word COM object"
    }
    catch {
        Write-Host "Word COM object not available. Consider using alternative methods below."
        Write-Host "Error: $($_.Exception.Message)"
    }
}
catch {
    Write-Host "Error during conversion: $($_.Exception.Message)"
    Write-Host "Please try one of the alternative methods listed in the documentation."
}

# Usage example:
# .\pdf-to-text-powershell.ps1 -PdfPath "Documentation\IDR_User_Guide_2024Q4_SP1.pdf"
