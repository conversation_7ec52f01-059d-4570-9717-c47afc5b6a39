"""
Integration tests for IDR XML Mapping Generator
"""

import pytest
import tempfile
import os
from pathlib import Path

from src.main import IDRXMLMappingGenerator
from src.core.idr_types import GenerationConfig, CoreStreamType


class TestIntegration:
    """Integration tests for the complete generation process"""
    
    @pytest.fixture
    def sample_insurance_xsd(self):
        """Sample insurance-related XSD for testing"""
        return '''<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://insurance.example.com/policy"
           xmlns:tns="http://insurance.example.com/policy"
           elementFormDefault="qualified">
           
  <!-- Policy root element -->
  <xs:element name="Policy" type="tns:PolicyType"/>
  
  <!-- Policy complex type -->
  <xs:complexType name="PolicyType">
    <xs:sequence>
      <xs:element name="PolicyNumber" type="xs:string"/>
      <xs:element name="ProductCode" type="xs:string"/>
      <xs:element name="IssueDate" type="xs:date"/>
      <xs:element name="PremiumAmount" type="xs:decimal"/>
      <xs:element name="SumAssured" type="xs:decimal"/>
      <xs:element name="PolicyStatus" type="tns:StatusType"/>
      <xs:element name="Insured" type="tns:InsuredType"/>
      <xs:element name="Benefits" type="tns:BenefitsType"/>
    </xs:sequence>
    <xs:attribute name="policyId" type="xs:int" use="required"/>
  </xs:complexType>
  
  <!-- Status enumeration -->
  <xs:simpleType name="StatusType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Active"/>
      <xs:enumeration value="Lapsed"/>
      <xs:enumeration value="Surrendered"/>
      <xs:enumeration value="Matured"/>
    </xs:restriction>
  </xs:simpleType>
  
  <!-- Insured person -->
  <xs:complexType name="InsuredType">
    <xs:sequence>
      <xs:element name="FirstName" type="xs:string"/>
      <xs:element name="LastName" type="xs:string"/>
      <xs:element name="DateOfBirth" type="xs:date"/>
      <xs:element name="Gender" type="xs:string"/>
      <xs:element name="Age" type="xs:int"/>
    </xs:sequence>
  </xs:complexType>
  
  <!-- Benefits -->
  <xs:complexType name="BenefitsType">
    <xs:sequence>
      <xs:element name="DeathBenefit" type="xs:decimal"/>
      <xs:element name="MaturityBenefit" type="xs:decimal"/>
      <xs:element name="SurrenderValue" type="xs:decimal"/>
      <xs:element name="LoanValue" type="xs:decimal" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  
  <!-- Standalone elements for lookup tables -->
  <xs:element name="ProductCode" type="xs:string"/>
  <xs:element name="RegionCode" type="xs:string"/>
  <xs:element name="AgentCode" type="xs:string"/>
  
</xs:schema>'''
    
    @pytest.fixture
    def temp_insurance_xsd(self, sample_insurance_xsd):
        """Create temporary insurance XSD file"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xsd', delete=False) as f:
            f.write(sample_insurance_xsd)
            temp_file = f.name
        
        yield temp_file
        os.unlink(temp_file)
    
    def test_complete_generation_process(self, temp_insurance_xsd):
        """Test complete generation from XSD to IDR XML"""
        config = GenerationConfig(
            auto_create_dimensions=True,
            auto_create_facts=True,
            enable_merge_operations=True,
            default_core_stream=CoreStreamType.IMP,
            enable_validation=True
        )
        
        generator = IDRXMLMappingGenerator(config)
        xml_output = generator.generate_from_xsd(temp_insurance_xsd, "InsuranceTransform")
        
        # Basic validation
        assert xml_output is not None
        assert len(xml_output) > 0
        assert "TransformDefinition" in xml_output
        assert "InsuranceTransform" in xml_output
        
        # Check for required sections
        assert "<Connections>" in xml_output
        assert "<Dimensions>" in xml_output
        assert "<Facts>" in xml_output
        assert "<Pipelines>" in xml_output
        
        # Check for generated dimensions
        assert "DimProduct" in xml_output or "DimRegion" in xml_output
        
        # Check for generated facts
        assert "Fact" in xml_output
        
        # Check for pipeline elements
        assert "Pipeline" in xml_output
        assert "InitialStage" in xml_output
    
    def test_components_generation(self, temp_insurance_xsd):
        """Test separate component generation"""
        config = GenerationConfig()
        generator = IDRXMLMappingGenerator(config)
        
        components = generator.generate_components_separately(temp_insurance_xsd)
        
        assert 'dimensions' in components
        assert 'facts' in components
        assert 'connections' in components
        
        # Validate dimensions XML
        dimensions_xml = components['dimensions']
        assert "<Dimensions>" in dimensions_xml
        assert "autoload" in dimensions_xml
        assert "type=" in dimensions_xml
        
        # Validate facts XML
        facts_xml = components['facts']
        assert "<Facts>" in facts_xml
        assert "<Variables>" in facts_xml
        assert "JobId" in facts_xml  # Standard variable
        
        # Validate connections XML
        connections_xml = components['connections']
        assert "<Connections>" in connections_xml
        assert "<SourceConnections>" in connections_xml
        assert "<TargetConnections>" in connections_xml
    
    def test_xml_validation(self, temp_insurance_xsd):
        """Test XML validation functionality"""
        generator = IDRXMLMappingGenerator()
        xml_output = generator.generate_from_xsd(temp_insurance_xsd, "TestTransform")
        
        # Validate generated XML
        issues = generator.validate_generated_xml(xml_output)
        
        # Should have no critical issues
        critical_issues = [issue for issue in issues if "missing" in issue.lower()]
        assert len(critical_issues) == 0, f"Critical validation issues: {critical_issues}"
    
    def test_different_core_streams(self, temp_insurance_xsd):
        """Test generation with different core stream types"""
        core_streams = [CoreStreamType.IMP, CoreStreamType.PRJM, CoreStreamType.STOM]
        
        for core_stream in core_streams:
            config = GenerationConfig(default_core_stream=core_stream)
            generator = IDRXMLMappingGenerator(config)
            
            xml_output = generator.generate_from_xsd(temp_insurance_xsd, f"Transform_{core_stream.value}")
            
            assert xml_output is not None
            assert core_stream.value in xml_output
            assert "TransformDefinition" in xml_output
    
    def test_merge_operations_enabled(self, temp_insurance_xsd):
        """Test generation with merge operations enabled"""
        config = GenerationConfig(enable_merge_operations=True)
        generator = IDRXMLMappingGenerator(config)
        
        xml_output = generator.generate_from_xsd(temp_insurance_xsd, "MergeTransform")
        
        # Should contain merge attributes
        assert 'merge="true"' in xml_output
        assert 'match="true"' in xml_output or 'merge="true"' in xml_output
    
    def test_validation_pipeline_generation(self, temp_insurance_xsd):
        """Test validation pipeline generation"""
        config = GenerationConfig(enable_validation=True)
        generator = IDRXMLMappingGenerator(config)
        
        xml_output = generator.generate_from_xsd(temp_insurance_xsd, "ValidationTransform")
        
        # Should contain validation elements
        assert 'validate="true"' in xml_output or "ValidationPipeline" in xml_output
    
    def test_error_handling(self):
        """Test error handling for invalid inputs"""
        generator = IDRXMLMappingGenerator()
        
        # Test with non-existent file
        with pytest.raises(Exception):
            generator.generate_from_xsd("nonexistent.xsd", "ErrorTransform")
    
    def test_empty_schema_handling(self):
        """Test handling of minimal/empty schemas"""
        minimal_xsd = '''<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="Root" type="xs:string"/>
</xs:schema>'''
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xsd', delete=False) as f:
            f.write(minimal_xsd)
            temp_file = f.name
        
        try:
            generator = IDRXMLMappingGenerator()
            xml_output = generator.generate_from_xsd(temp_file, "MinimalTransform")
            
            # Should still generate valid IDR XML with standard components
            assert xml_output is not None
            assert "TransformDefinition" in xml_output
            assert "JobId" in xml_output  # Standard variables should be present
            
        finally:
            os.unlink(temp_file)
    
    def test_complex_schema_handling(self, temp_insurance_xsd):
        """Test handling of complex schemas with multiple levels"""
        # The insurance XSD is already complex, test it thoroughly
        generator = IDRXMLMappingGenerator()
        xml_output = generator.generate_from_xsd(temp_insurance_xsd, "ComplexTransform")
        
        # Should handle nested complex types
        assert "PolicyType" in xml_output or "Policy" in xml_output
        assert "InsuredType" in xml_output or "Insured" in xml_output
        
        # Should generate appropriate dimensions and facts
        lines = xml_output.split('\n')
        dimension_lines = [line for line in lines if '<Dimension' in line]
        fact_lines = [line for line in lines if '<Fact' in line]
        
        assert len(dimension_lines) > 0, "Should generate at least one dimension"
        assert len(fact_lines) > 0, "Should generate at least one fact"
    
    def test_naming_conventions(self, temp_insurance_xsd):
        """Test different naming conventions"""
        config = GenerationConfig(naming_convention="PascalCase")
        generator = IDRXMLMappingGenerator(config)
        
        xml_output = generator.generate_from_xsd(temp_insurance_xsd, "NamingTest")
        
        # Check for PascalCase naming
        assert "DimTime" in xml_output  # Standard dimension
        assert "FactProphet" in xml_output or "Fact" in xml_output
        
        # Variable names should be properly formatted
        assert "JobId" in xml_output
        assert "RunNumber" in xml_output
    
    def test_documentation_inclusion(self, temp_insurance_xsd):
        """Test inclusion of documentation in generated XML"""
        config = GenerationConfig(include_documentation=True)
        generator = IDRXMLMappingGenerator(config)
        
        xml_output = generator.generate_from_xsd(temp_insurance_xsd, "DocumentedTransform")
        
        # Should include XML comments or documentation
        # The exact format depends on implementation
        assert "<!--" in xml_output or "documentation" in xml_output.lower()
    
    def test_large_schema_performance(self):
        """Test performance with larger schemas"""
        # Create a larger XSD with many elements
        large_xsd_elements = []
        for i in range(50):
            large_xsd_elements.append(f'<xs:element name="Field{i}" type="xs:string"/>')
        
        large_xsd = f'''<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="LargeType">
    <xs:sequence>
      {"".join(large_xsd_elements)}
    </xs:sequence>
  </xs:complexType>
  <xs:element name="LargeElement" type="LargeType"/>
</xs:schema>'''
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xsd', delete=False) as f:
            f.write(large_xsd)
            temp_file = f.name
        
        try:
            import time
            start_time = time.time()
            
            generator = IDRXMLMappingGenerator()
            xml_output = generator.generate_from_xsd(temp_file, "LargeTransform")
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Should complete in reasonable time (less than 30 seconds)
            assert processing_time < 30, f"Processing took too long: {processing_time} seconds"
            assert xml_output is not None
            assert "TransformDefinition" in xml_output
            
        finally:
            os.unlink(temp_file)
