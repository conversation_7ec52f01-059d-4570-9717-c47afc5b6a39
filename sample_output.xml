<?xml version="1.0" encoding="utf-8"?>
<TransformDefinition>
  <Name>SampleTransform</Name>
  <Connections>
    <SourceConnections>
      <Connection>S1</Connection>
    </SourceConnections>
    <TargetConnections>
      <Connection>T1</Connection>
    </TargetConnections>
  </Connections>
  <Dimensions>
    <Dimension autoload="true" dynamic="false">
      <Name>DimTime</Name>
      <TableName>DimTime</TableName>
      <Connection>T1</Connection>
      <Key type="int">MonthId</Key>
      <Id>TimeId</Id>
      <UsableColumns>
        <UsableColumn type="int">Year</UsableColumn>
        <UsableColumn type="int">Month</UsableColumn>
        <UsableColumn type="int">Quarter</UsableColumn>
        <UsableColumn type="string">MonthName</UsableColumn>
      </UsableColumns>
    </Dimension>
    <Dimension autoload="true" dynamic="true">
      <Name>DimRun</Name>
      <TableName>DimRun</TableName>
      <Connection>T1</Connection>
      <Key type="int">RunNumber</Key>
      <Id>RunId</Id>
      <UsableColumns>
        <UsableColumn type="string">RunName</UsableColumn>
        <UsableColumn type="string">RunDate</UsableColumn>
        <UsableColumn type="string">RunType</UsableColumn>
      </UsableColumns>
    </Dimension>
    <Dimension autoload="true" dynamic="true">
      <Name>DimSPCode</Name>
      <TableName>DimSPCode</TableName>
      <Connection>T1</Connection>
      <Key type="int">SPCode</Key>
      <Id>SPCodeId</Id>
      <UsableColumns>
        <UsableColumn type="string">SPCodeName</UsableColumn>
        <UsableColumn type="string">SPCodeType</UsableColumn>
      </UsableColumns>
    </Dimension>
  </Dimensions>
  <Facts>
    <Fact>
      <Name>FactProphetResults</Name>
      <TableName>FactProphetResults</TableName>
      <Connection>T1</Connection>
      <Variables>
        <Variable type="int">JobId</Variable>
        <Variable type="int">RunNumber</Variable>
        <Variable type="int">SPCode</Variable>
        <Variable type="int">MonthId</Variable>
        <Variable type="int">ProductId</Variable>
        <Variable type="double">DEATH_OUTGO</Variable>
        <Variable type="double">SURR_OUTGO</Variable>
        <Variable type="double">ANNUAL_PREM</Variable>
        <Variable type="double">RESERVES</Variable>
        <Variable type="double">CASH_FLOW</Variable>
        <Variable type="double">PRESENT_VALUE</Variable>
        <Variable type="double">NEW_BUSINESS</Variable>
        <Variable type="int">POLICY_COUNT</Variable>
      </Variables>
    </Fact>
  </Facts>
  <Pipelines>
    <Pipeline>
      <Name>SamplePipeline</Name>
      <InitialStage>
        <Name>InitialStage</Name>
        <CoreStream>IMP</CoreStream>
        <Filters>
          <RunNumbers>
            <Value>1</Value>
          </RunNumbers>
        </Filters>
        <StreamVariables>
          <StreamVariable type="double">
            <VariableName>ANNUAL_PREM</VariableName>
            <SourceVariable>ANNUAL_PREM</SourceVariable>
          </StreamVariable>
        </StreamVariables>
        <Assignments>
          <Assignment type="int">
            <VariableName>JobId</VariableName>
            <Key>JobId</Key>
          </Assignment>
          <Assignment type="int">
            <VariableName>RunNumber</VariableName>
            <Key>RunNumber</Key>
          </Assignment>
        </Assignments>
        <Targets>
          <Target>
            <Fact>FactProphetResults</Fact>
          </Target>
        </Targets>
      </InitialStage>
    </Pipeline>
  </Pipelines>
</TransformDefinition>