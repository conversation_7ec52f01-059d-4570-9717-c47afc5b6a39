#!/usr/bin/env python3
"""
Simple test script for IDR XML Mapping Generator
"""

import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Now import our modules
from core.schema_parser import XSDParser, TypeMapper
from core.idr_types import GenerationConfig, CoreStreamType, IDRDimension, IDRFact
from generators.dimension_builder import DimensionBuilder
from generators.fact_builder import FactBuilder
from generators.xml_generator import XMLGenerator


def test_type_mapper():
    """Test the type mapper"""
    print("Testing Type Mapper...")
    
    # Test basic mappings
    assert TypeMapper.map_xsd_to_idr("string") == "string"
    assert TypeMapper.map_xsd_to_idr("int") == "int"
    assert TypeMapper.map_xsd_to_idr("decimal") == "double"
    assert TypeMapper.map_xsd_to_idr("boolean") == "bool"
    
    # Test with namespace prefixes
    assert TypeMapper.map_xsd_to_idr("xs:string") == "string"
    assert TypeMapper.map_xsd_to_idr("xsd:int") == "int"
    
    print("✓ Type Mapper tests passed")


def test_dimension_builder():
    """Test dimension builder"""
    print("Testing Dimension Builder...")
    
    config = GenerationConfig()
    builder = DimensionBuilder(config)
    
    # Test standard dimensions
    standard_dims = builder.create_standard_dimensions()
    assert len(standard_dims) > 0
    
    # Check for expected dimensions
    dim_names = [dim.name for dim in standard_dims]
    assert "DimTime" in dim_names
    assert "DimRun" in dim_names
    assert "DimSPCode" in dim_names
    
    print(f"✓ Created {len(standard_dims)} standard dimensions")


def test_fact_builder():
    """Test fact builder"""
    print("Testing Fact Builder...")
    
    config = GenerationConfig()
    builder = FactBuilder(config)
    
    # Test Prophet fact creation
    prophet_fact = builder.create_prophet_fact()
    assert prophet_fact.name == "FactProphetResults"
    assert len(prophet_fact.variables) > 0
    
    # Check for standard variables
    var_names = [var.name for var in prophet_fact.variables]
    assert "JobId" in var_names
    assert "RunNumber" in var_names
    assert "SPCode" in var_names
    assert "MonthId" in var_names
    
    print(f"✓ Created Prophet fact with {len(prophet_fact.variables)} variables")


def test_xml_generation():
    """Test XML generation"""
    print("Testing XML Generation...")
    
    # Create sample dimension
    from core.idr_types import IDRDimension, UsableColumn
    
    dimension = IDRDimension(
        name="DimProduct",
        table_name="DimProduct", 
        connection="T1",
        key_column="ProductName",
        key_type="string",
        id_column="ProductId",
        usable_columns=[
            UsableColumn("Region", "string", "Product region"),
            UsableColumn("Factor", "double", "Product factor")
        ],
        autoload=True,
        dynamic=False
    )
    
    # Generate XML
    generator = XMLGenerator()
    xml_output = generator.generate_dimensions_xml([dimension])
    
    # Basic validation
    assert "<Dimensions>" in xml_output
    assert "DimProduct" in xml_output
    assert 'autoload="true"' in xml_output
    assert 'type="string"' in xml_output
    assert 'type="double"' in xml_output
    
    print("✓ XML generation successful")
    print(f"Generated XML length: {len(xml_output)} characters")


def create_sample_xml():
    """Create a complete sample XML"""
    print("Creating Complete Sample XML...")
    
    from core.idr_types import (
        TransformDefinition, IDRConnection, ConnectionType,
        IDRPipeline, PipelineStage, TimeRange, Filter, Target,
        StreamVariable, Assignment, AssignmentSourceType, CoreStreamType
    )
    
    # Create connections
    source_connections = [
        IDRConnection("S1", ConnectionType.IDR, is_source=True)
    ]
    target_connections = [
        IDRConnection("T1", ConnectionType.IDR, is_source=False)
    ]
    
    # Create dimension
    config = GenerationConfig()
    dim_builder = DimensionBuilder(config)
    dimensions = dim_builder.create_standard_dimensions()
    
    # Create fact
    fact_builder = FactBuilder(config)
    facts = [fact_builder.create_prophet_fact()]
    
    # Create simple pipeline
    pipeline = IDRPipeline(
        name="SamplePipeline",
        initial_stage=PipelineStage(
            name="InitialStage",
            core_stream=CoreStreamType.IMP,
            filters=[Filter("RunNumbers", ["1"])],
            stream_variables=[
                StreamVariable("ANNUAL_PREM", "double", "ANNUAL_PREM", autoassign=True)
            ],
            assignments=[
                Assignment("JobId", "int", AssignmentSourceType.KEY, "JobId"),
                Assignment("RunNumber", "int", AssignmentSourceType.KEY, "RunNumber")
            ],
            targets=[Target("FactProphetResults")]
        ),
        validation_enabled=False
    )
    
    # Create transform definition
    transform_def = TransformDefinition(
        name="SampleTransform",
        source_connections=source_connections,
        target_connections=target_connections,
        dimensions=dimensions,
        facts=facts,
        pipelines=[pipeline]
    )
    
    # Generate XML
    generator = XMLGenerator()
    xml_output = generator.generate_xml(transform_def)
    
    # Save to file
    output_file = Path("sample_output.xml")
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(xml_output)
    
    print(f"✓ Complete XML generated: {output_file}")
    print(f"XML length: {len(xml_output)} characters")
    
    # Basic validation
    required_sections = ["TransformDefinition", "Connections", "Dimensions", "Facts", "Pipelines"]
    for section in required_sections:
        if section in xml_output:
            print(f"✓ Contains {section}")
        else:
            print(f"✗ Missing {section}")
    
    return xml_output


def main():
    """Run all tests"""
    print("IDR XML Mapping Generator - Test Suite")
    print("=" * 50)
    
    try:
        test_type_mapper()
        print()
        
        test_dimension_builder()
        print()
        
        test_fact_builder()
        print()
        
        test_xml_generation()
        print()
        
        xml_output = create_sample_xml()
        print()
        
        print("=" * 50)
        print("All tests completed successfully!")
        print("=" * 50)
        
        # Show first few lines of generated XML
        print("\nSample of generated XML:")
        print("-" * 30)
        lines = xml_output.split('\n')
        for i, line in enumerate(lines[:15]):
            print(f"{i+1:2d}: {line}")
        if len(lines) > 15:
            print(f"... ({len(lines) - 15} more lines)")
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
