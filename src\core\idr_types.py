"""
IDR Stream Mode Data Types and Structures
Based on IDR User Guide 2024Q4 SP1 examples from lines 4200-4750
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from enum import Enum


class CoreStreamType(Enum):
    """Core stream types from User Guide lines 4746-4800"""
    PRJM = "PRJM"  # Monthly projections
    PRJY = "PRJY"  # Yearly projections
    STOM = "STOM"  # Monthly stochastic
    STOY = "STOY"  # Yearly stochastic
    IMP = "IMP"    # Individual model points
    IDRM = "IDRM"  # IDR monthly (for IDR-to-IDR)
    TEMP_STRING = "TEMP-STRING"  # Temporary table core stream


class ConnectionType(Enum):
    """Connection types from User Guide Chapter 6"""
    IDR = "IDR"
    TEXT = "TEXT"
    CSV = "CSV"
    AWS_S3 = "AWS S3"
    SNOWFLAKE = "SNOWFLAKE"
    PRD = "PRD"


class AssignmentSourceType(Enum):
    """Assignment source types from lines 4956-4970"""
    KEY = "key"
    STREAM = "stream"
    EXPRESSION = "expression"
    GLOBAL = "global"


@dataclass
class IDRConnection:
    """IDR Connection definition from lines 4224-4233"""
    name: str
    connection_type: ConnectionType
    is_source: bool = True
    parameters: Dict[str, str] = field(default_factory=dict)


@dataclass
class UsableColumn:
    """Usable column for dimensions from lines 4253-4260"""
    name: str
    type: str
    description: Optional[str] = None


@dataclass
class IDRDimension:
    """IDR Dimension definition from lines 4246-4270"""
    name: str
    table_name: str
    connection: str
    key_column: str
    key_type: str
    id_column: str
    usable_columns: List[UsableColumn] = field(default_factory=list)
    autoload: bool = False
    dynamic: bool = False


@dataclass
class IDRVariable:
    """IDR Variable definition from lines 4323-4335"""
    name: str
    type: str
    is_match: bool = False  # For merge operations
    description: Optional[str] = None


@dataclass
class IDRFact:
    """IDR Fact definition from lines 4317-4340"""
    name: str
    table_name: str
    connection: str
    variables: List[IDRVariable] = field(default_factory=list)
    merge_enabled: bool = False


@dataclass
class StreamVariable:
    """Stream variable from lines 4857-4890"""
    variable_name: str
    variable_type: str
    source_variable: str
    autoassign: bool = True
    aggregate_function: Optional[str] = None
    index_range: Optional[str] = None  # For array variables


@dataclass
class Assignment:
    """Assignment definition from lines 4956-4970"""
    variable_name: str
    variable_type: str
    source_type: AssignmentSourceType
    source_value: str
    filter_expression: Optional[str] = None
    is_expression: bool = False


@dataclass
class Filter:
    """Filter definition from lines 5203-5215"""
    filter_type: str  # RunNumbers, Products, Connections, etc.
    values: List[str]


@dataclass
class TimeRange:
    """Time range for pipelines from lines 4803-4807"""
    first: int = 0
    times: int = 1


@dataclass
class Target:
    """Target definition for pipeline stages"""
    fact_name: str
    filter_expression: Optional[str] = None
    skip_when_zero: bool = False


@dataclass
class PipelineStage:
    """Pipeline stage definition"""
    name: str
    core_stream: Optional[CoreStreamType] = None
    time_range: Optional[TimeRange] = None
    filters: List[Filter] = field(default_factory=list)
    stream_variables: List[StreamVariable] = field(default_factory=list)
    assignments: List[Assignment] = field(default_factory=list)
    targets: List[Target] = field(default_factory=list)
    key_properties: List[Dict[str, str]] = field(default_factory=list)


@dataclass
class IDRPipeline:
    """IDR Pipeline definition from lines 4739-4750"""
    name: str
    initial_stage: PipelineStage
    additional_stages: List[PipelineStage] = field(default_factory=list)
    validation_enabled: bool = False


@dataclass
class TemporaryTable:
    """Temporary table definition from lines 4441-4451"""
    name: str
    key_column: str
    key_type: str
    id_column: str
    id_type: str
    usable_columns: List[UsableColumn] = field(default_factory=list)


@dataclass
class TransformDefinition:
    """Complete Transform Definition from lines 4201-4220"""
    name: str
    source_connections: List[IDRConnection] = field(default_factory=list)
    target_connections: List[IDRConnection] = field(default_factory=list)
    dimensions: List[IDRDimension] = field(default_factory=list)
    facts: List[IDRFact] = field(default_factory=list)
    temporary_tables: List[TemporaryTable] = field(default_factory=list)
    pipelines: List[IDRPipeline] = field(default_factory=list)
    source_streams: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class ValidationRule:
    """Validation rule for generated mappings"""
    rule_type: str
    description: str
    severity: str  # Error, Warning, Info
    check_function: str


@dataclass
class GenerationConfig:
    """Configuration for XML generation"""
    auto_create_dimensions: bool = True
    auto_create_facts: bool = True
    enable_merge_operations: bool = False
    default_core_stream: CoreStreamType = CoreStreamType.IMP
    enable_validation: bool = True
    naming_convention: str = "PascalCase"  # PascalCase, camelCase, snake_case
    include_documentation: bool = True


class IDRNamingConventions:
    """Naming conventions for IDR objects"""
    
    @staticmethod
    def to_pascal_case(name: str) -> str:
        """Convert to PascalCase"""
        return ''.join(word.capitalize() for word in name.replace('_', ' ').replace('-', ' ').split())
    
    @staticmethod
    def to_camel_case(name: str) -> str:
        """Convert to camelCase"""
        pascal = IDRNamingConventions.to_pascal_case(name)
        return pascal[0].lower() + pascal[1:] if pascal else ""
    
    @staticmethod
    def to_snake_case(name: str) -> str:
        """Convert to snake_case"""
        import re
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
    
    @staticmethod
    def sanitize_name(name: str) -> str:
        """Sanitize name for IDR usage"""
        import re
        # Remove invalid characters and replace with underscore
        sanitized = re.sub(r'[^a-zA-Z0-9_]', '_', name)
        # Ensure it starts with a letter
        if sanitized and sanitized[0].isdigit():
            sanitized = 'Field_' + sanitized
        return sanitized or 'UnnamedField'
    
    @staticmethod
    def generate_dimension_name(element_name: str) -> str:
        """Generate dimension name from element name"""
        base_name = IDRNamingConventions.sanitize_name(element_name)
        return f"Dim{IDRNamingConventions.to_pascal_case(base_name)}"
    
    @staticmethod
    def generate_fact_name(element_name: str) -> str:
        """Generate fact name from element name"""
        base_name = IDRNamingConventions.sanitize_name(element_name)
        return f"Fact{IDRNamingConventions.to_pascal_case(base_name)}"
    
    @staticmethod
    def generate_table_name(element_name: str, is_dimension: bool = False) -> str:
        """Generate table name from element name"""
        base_name = IDRNamingConventions.sanitize_name(element_name)
        prefix = "Dim" if is_dimension else "Fact"
        return f"{prefix}{IDRNamingConventions.to_pascal_case(base_name)}"


class IDRValidationRules:
    """Validation rules for IDR Stream mode"""
    
    STANDARD_RULES = [
        ValidationRule(
            rule_type="dimension_key_unique",
            description="Dimension key columns must be unique",
            severity="Error",
            check_function="check_dimension_key_uniqueness"
        ),
        ValidationRule(
            rule_type="fact_variables_typed",
            description="All fact variables must have valid types",
            severity="Error",
            check_function="check_fact_variable_types"
        ),
        ValidationRule(
            rule_type="pipeline_targets_exist",
            description="Pipeline targets must reference existing facts",
            severity="Error",
            check_function="check_pipeline_target_references"
        ),
        ValidationRule(
            rule_type="connection_references_valid",
            description="All connection references must be defined",
            severity="Error",
            check_function="check_connection_references"
        ),
        ValidationRule(
            rule_type="stream_variables_mapped",
            description="Stream variables should have corresponding assignments",
            severity="Warning",
            check_function="check_stream_variable_assignments"
        )
    ]
