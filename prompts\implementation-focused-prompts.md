# Implementation-Focused Prompts for IDR XML Mapping Generator
## Building Production-Ready Code from IDR User Guide 2024Q4 SP1

### Overview
These prompts focus on translating the IDR User Guide content into working code for your XML mapping generator. They emphasize practical implementation, code generation, and production deployment.

---

## Section 1: Code Architecture and Design

### Prompt 1.1: Design XML Mapping Generator Architecture
```
Using the IDR User Guide text file, design a comprehensive architecture for an IDR XML mapping generator:

1. **Core Components Analysis** (from Chapter 4, lines 4200-4220):
   - Based on the TransformDefinition structure, design classes for:
     * ConnectionManager (handles SourceConnections/TargetConnections)
     * DimensionBuilder (creates Dimension XML from schemas)
     * FactBuilder (generates Fact tables and Variables)
     * PipelineGenerator (creates Pipelines with stages)
   
2. **XML Schema Processing** (inferred from Stream mode structure):
   - Design XSDParser class to analyze XML schemas
   - Create TypeMapper to convert XSD types to IDR types
   - Build ConstraintAnalyzer to extract validation rules
   - Design NamespaceResolver for complex schema handling

3. **Template Engine Design** (based on XML examples throughout Chapter 4):
   - Create XMLTemplateEngine using examples from lines 4246-4270 (Dimensions)
   - Build ConfigurationGenerator using patterns from lines 4317-4340 (Facts)
   - Design PipelineTemplateBuilder using examples from lines 4739-4750

4. **Integration Layer** (from Chapter 5 command line examples):
   - Create CommandLineBuilder for IDR execution
   - Design ConnectionStringBuilder for different target types
   - Build ValidationEngine using temporary table patterns (lines 4599-4650)

Provide a complete class diagram and component interaction design.
```

### Prompt 1.2: Design Data Flow and Processing Pipeline
```
From the IDR User Guide text file, design the data flow for your XML mapping generator:

1. **Input Processing Flow** (based on Stream mode requirements):
   - XSD Schema Analysis → Type Mapping → Structure Extraction
   - Business Rule Extraction → Validation Rule Generation
   - Namespace Processing → Element Relationship Mapping

2. **XML Generation Flow** (using examples from Chapter 4):
   - TransformDefinition Creation (lines 4200-4220)
   - Connection Configuration (lines 4224-4233)
   - Dimension Generation (lines 4246-4270)
   - Fact Table Creation (lines 4317-4340)
   - Pipeline Assembly (lines 4739-4750)

3. **Validation and Testing Flow** (from validation examples):
   - XML Schema Validation
   - IDR Compatibility Checking
   - Test Data Generation using temporary tables (lines 4453-4590)
   - Pipeline Validation using ~~Validation~~ table (lines 4599-4650)

4. **Output and Deployment Flow** (from Chapter 5):
   - Command Line Generation
   - Configuration File Creation
   - Deployment Script Generation
   - Monitoring Setup

Design the complete data flow with error handling and rollback capabilities.
```

---

## Section 2: Core Implementation Components

### Prompt 2.1: Implement XSD to IDR Type Mapping Engine
```
Using the IDR User Guide text file, implement a comprehensive type mapping engine:

1. **Basic Type Mapping** (from Variable type examples in Chapter 4):
   - Extract all type="..." examples from the text (int, double, string, etc.)
   - Map XSD primitive types to IDR types:
     * xs:string → type="string"
     * xs:int → type="int" 
     * xs:decimal → type="double"
     * xs:boolean → type="bool"
     * xs:date → type="string" (with format handling)

2. **Complex Type Handling** (based on Dimension and Fact structures):
   - Map XSD complex types to IDR Dimension structures
   - Convert XSD elements to Fact Variables
   - Handle XSD attributes as UsableColumns (lines 4253-4260)

3. **Constraint Mapping** (inferred from validation patterns):
   - Convert XSD restrictions to IDR expressions
   - Map minOccurs/maxOccurs to validation rules
   - Transform XSD patterns to Filter expressions (lines 5203-5215)

4. **Advanced Mapping Features**:
   - Handle XSD choice elements
   - Process XSD union types
   - Manage XSD inheritance relationships

Implement complete type mapping with validation and error handling.
```

### Prompt 2.2: Build XML Template Generation Engine
```
From the IDR User Guide text file, create a template generation engine:

1. **TransformDefinition Template** (using structure from lines 4200-4220):
   ```csharp
   public class TransformDefinitionBuilder
   {
       public string GenerateTransformDefinition(SchemaAnalysis schema)
       {
           // Use the exact structure from the user guide
           // Include all sections: Connections, Dimensions, Facts, Pipelines
       }
   }
   ```

2. **Dimension Template Generator** (based on examples from lines 4246-4270):
   - Create templates for autoload="true" dimensions
   - Generate dynamic="false" standard dimensions
   - Build UsableColumns from XSD attributes
   - Handle Key and Id column generation

3. **Fact Template Generator** (using patterns from lines 4317-4340):
   - Generate Variables from XSD elements
   - Create merge="true" facts when needed
   - Handle TableName generation with naming conventions
   - Build Variable lists with proper types

4. **Pipeline Template Generator** (from examples in lines 4739-4750):
   - Create InitialStage with appropriate CoreStream
   - Generate StreamVariables from schema elements
   - Build Assignments for data mapping
   - Create Targets linking to Facts

Implement with full XML generation and validation capabilities.
```

---

## Section 3: Advanced Feature Implementation

### Prompt 3.1: Implement Stream Mode Pipeline Generator
```
Using the IDR User Guide text file, implement advanced pipeline generation:

1. **CoreStream Selection Logic** (from Chapter 4 core stream examples):
   - Analyze data characteristics to choose appropriate CoreStream
   - Implement logic for PRJM, PRJY, STOM, STOY, IMP selection
   - Handle TimeRange generation for time-dependent streams (lines 4803-4807)

2. **Multi-Stage Pipeline Generation** (based on grouping examples):
   - Implement grouping logic using KeyProperties
   - Generate subsequent stages for data aggregation
   - Handle custom stream creation for complex grouping

3. **StreamVariable Auto-Generation** (using examples from lines 4857-4890):
   - Implement autoassign="true" logic
   - Generate appropriate Aggregate functions
   - Handle array variables with index1 attributes (lines 4892-4910)

4. **Assignment Generation** (from examples in lines 4956-4970):
   - Create source="key" assignments for dimension lookups
   - Generate source="stream" assignments for data mapping
   - Build source="expression" assignments for calculations

Implement with support for complex data transformation scenarios.
```

### Prompt 3.2: Build Validation and Error Handling System
```
From the IDR User Guide text file, implement comprehensive validation:

1. **XML Schema Validation** (based on Stream mode requirements):
   - Validate generated XML against IDR schema requirements
   - Check TransformDefinition structure completeness
   - Verify all required sections are present

2. **IDR-Specific Validation** (using validation examples from lines 4599-4650):
   - Implement ~~Validation~~ temporary table integration
   - Generate validation pipelines with validate="true"
   - Create Key, Level, Message assignment patterns

3. **Business Rule Validation**:
   - Validate dimension key uniqueness
   - Check fact table variable consistency
   - Verify pipeline flow integrity

4. **Runtime Validation** (using predefined temporary tables):
   - Implement ~~Count~Detail~~ monitoring
   - Use ~~Output~~ table for result verification
   - Create error detection and reporting mechanisms

Build comprehensive validation with detailed error reporting and correction suggestions.
```

---

## Section 4: Integration and Deployment

### Prompt 4.1: Implement Command Line Integration
```
Using the IDR User Guide text file, implement IDR command line integration:

1. **Command Builder** (from Chapter 5 examples around lines 7935-8000):
   - Generate complete IDR command lines
   - Handle sourceconnection and targetconnection parameters
   - Implement vault file integration for secure connections

2. **Connection String Generation** (from Chapter 6 examples):
   - Build Text target connections (lines 11020-11140)
   - Generate AWS S3 connections (lines 11134-11180)
   - Create Snowflake connections (lines 11212-11275)

3. **Parameter Management**:
   - Handle Stream mode specific parameters
   - Implement compatibility mode settings
   - Manage global values and configuration overrides

4. **Execution and Monitoring**:
   - Launch IDR processes
   - Monitor execution progress
   - Capture and parse execution results

Implement with full error handling and logging capabilities.
```

### Prompt 4.2: Build Testing and Quality Assurance Framework
```
From the IDR User Guide text file, implement comprehensive testing:

1. **Unit Testing Framework** (based on validation patterns):
   - Test individual component generation
   - Validate XML structure correctness
   - Verify type mapping accuracy

2. **Integration Testing** (using temporary table examples):
   - Test complete XML generation pipeline
   - Validate IDR execution with generated XML
   - Use ~~Count~Detail~~ for data validation (lines 4453-4590)

3. **Performance Testing**:
   - Benchmark XML generation speed
   - Test with large schema files
   - Validate memory usage patterns

4. **End-to-End Testing** (using complete examples):
   - Test full schema-to-execution pipeline
   - Validate data accuracy and completeness
   - Test error handling and recovery

Build automated testing with comprehensive coverage and reporting.
```

---

## Section 5: Production Deployment and Maintenance

### Prompt 5.1: Design Production Deployment Strategy
```
Using the IDR User Guide text file, design production deployment:

1. **Configuration Management** (based on connection examples):
   - Implement environment-specific configurations
   - Handle vault file management for different environments
   - Manage connection string variations

2. **Deployment Automation**:
   - Create deployment scripts for different environments
   - Implement configuration validation before deployment
   - Build rollback capabilities for failed deployments

3. **Monitoring and Alerting** (using validation table patterns):
   - Implement monitoring using IDR logging capabilities
   - Create alerts based on validation failures
   - Build dashboards for system health monitoring

4. **Maintenance and Updates**:
   - Design schema change management
   - Implement version control for generated configurations
   - Build update and migration procedures

Create comprehensive production deployment with full operational support.
```

### Prompt 5.2: Implement Documentation and User Interface
```
From the IDR User Guide text file, implement user-facing components:

1. **Documentation Generation**:
   - Auto-generate documentation for created mappings
   - Create user guides for generated configurations
   - Build troubleshooting guides with common issues

2. **User Interface Design** (inspired by Chapter 8 UI examples):
   - Create schema upload and analysis interface
   - Build configuration review and editing capabilities
   - Implement testing and validation interfaces

3. **API Design**:
   - Create REST APIs for programmatic access
   - Implement batch processing capabilities
   - Build integration APIs for external systems

4. **Support Tools**:
   - Create diagnostic tools for troubleshooting
   - Build performance analysis tools
   - Implement configuration comparison utilities

Build comprehensive user experience with full documentation and support.
```

---

## Implementation Guidelines

### Development Approach
1. **Incremental Development**: Build and test each component separately
2. **Template-Driven**: Use actual examples from the user guide as templates
3. **Validation-First**: Implement validation at each step
4. **Production-Ready**: Design for scalability and maintainability

### Quality Assurance
1. **Test-Driven Development**: Write tests based on user guide examples
2. **Continuous Integration**: Automate testing and validation
3. **Performance Monitoring**: Track generation speed and resource usage
4. **Error Handling**: Comprehensive error detection and recovery

### Documentation Strategy
1. **Code Documentation**: Document all classes and methods
2. **User Documentation**: Create comprehensive user guides
3. **API Documentation**: Document all interfaces and APIs
4. **Troubleshooting Guides**: Create problem-solving resources

### Expected Deliverables
After implementing these prompts, you should have:
- Complete XML mapping generator with all features
- Comprehensive testing framework
- Production deployment capabilities
- Full documentation and user interfaces
- Monitoring and maintenance tools
