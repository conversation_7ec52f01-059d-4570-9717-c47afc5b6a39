"""
Setup script for IDR XML Mapping Generator
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
if requirements_file.exists():
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
else:
    requirements = [
        'lxml>=4.9.0',
        'xmlschema>=2.0.0',
        'defusedxml>=0.7.1'
    ]

setup(
    name="idr-xml-mapping-generator",
    version="1.0.0",
    description="Generate IDR XML mappings from XSD schemas for IDR Stream mode",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="IDR Development Team",
    author_email="<EMAIL>",
    url="https://github.com/example/idr-xml-mapping-generator",
    
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    
    python_requires=">=3.8",
    install_requires=requirements,
    
    extras_require={
        'dev': [
            'pytest>=7.0.0',
            'pytest-cov>=4.0.0',
            'black>=22.0.0',
            'flake8>=5.0.0',
            'mypy>=1.0.0',
        ],
        'docs': [
            'sphinx>=5.0.0',
            'sphinx-rtd-theme>=1.0.0',
        ]
    },
    
    entry_points={
        'console_scripts': [
            'idr-xml-generator=main:main',
        ],
    },
    
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Financial and Insurance Industry",
        "Topic :: Software Development :: Code Generators",
        "Topic :: Text Processing :: Markup :: XML",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
    
    keywords="idr xml mapping generator xsd schema insurance actuarial prophet",
    
    project_urls={
        "Bug Reports": "https://github.com/example/idr-xml-mapping-generator/issues",
        "Source": "https://github.com/example/idr-xml-mapping-generator",
        "Documentation": "https://idr-xml-mapping-generator.readthedocs.io/",
    },
    
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.xml", "*.xsd"],
    },
    
    zip_safe=False,
)
