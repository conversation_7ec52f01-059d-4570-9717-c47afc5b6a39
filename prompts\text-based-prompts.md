# IDR XML Mapping Generator Prompts - Text-Based Version
## Leveraging IDR User Guide 2024Q4 SP1 Text Format for Stream Mode Implementation

### Overview
These prompts are specifically designed to work with the text version of the IDR User Guide 2024Q4 SP1. The text file contains 17,706 lines with clear page markers and section headers that make it easy to reference specific content.

### Key Stream Mode Sections in the Text File
- **Chapter 4**: "Mapping data using XML in stream mode" (starts around line 4110)
- **Chapter 5**: Stream mode command line parameters (around line 7935)
- **Chapter 6**: Stream mode target connections (around line 10771)
- **Chapter 8**: Stream mode user interface (around line 15014)

---

## Section 1: Architecture and Foundation Prompts

### Prompt 1.1: Stream Mode Core Concepts
```
Using the IDR User Guide 2024Q4 SP1 text file (Documentation/IDR_User_Guide_2024Q4_SP1.txt), extract comprehensive information about IDR Stream mode from Chapter 4 (starting around line 4110):

1. **Stream Mode Introduction** (around lines 4138-4150):
   - What is IDR Stream mode and how does it differ from traditional mode?
   - What are the key capabilities introduced in IDR 2022 Q1?
   - What target connection types are supported?

2. **XML Structure** (around lines 4200-4220):
   - What is the root XML tag for Stream mode (<TransformDefinition>)?
   - What are the main sections: Connections, Dimensions, Facts, TemporaryTables, SourceStreams, Pipelines?
   - How does this differ from traditional mode structure?

3. **Core Components** (lines 4235-4240):
   - How do dimensions work in Stream mode?
   - What are "ID" columns and "Key" columns?
   - What are "usable columns"?

Please provide specific line references and exact quotes from the text file for each concept.
```

### Prompt 1.2: Stream Mode XML Mapping Structure
```
From the IDR User Guide text file, analyze the Stream mode XML mapping structure detailed in Chapter 4:

1. **TransformDefinition Root** (around line 4201):
   - Extract the complete XML structure template
   - Identify all required and optional sections

2. **Connections Section** (lines 4205-4233):
   - How are source and target connections defined?
   - What's the difference between SourceConnections and TargetConnections?
   - Provide the exact XML syntax examples

3. **Dimensions Section** (lines 4235-4270):
   - Extract the complete Dimension XML structure
   - What attributes are available (autoload, dynamic)?
   - How are Key, Id, and UsableColumns defined?

4. **Facts Section** (lines 4313-4376):
   - How do Facts differ from traditional mode?
   - What's the structure for Variables within Facts?
   - How are TableName and Connection specified?

Reference specific line numbers and provide exact XML examples from the text.
```

---

## Section 2: Technical Implementation Prompts

### Prompt 2.1: Stream Mode Pipelines Deep Dive
```
Using the IDR User Guide text file, extract detailed information about Stream mode Pipelines from Chapter 4:

1. **Pipeline Structure** (around lines 4735-4750):
   - What are Pipelines and how do they work?
   - What stages are available (currently single-stage, multi-stage coming)?
   - Extract the basic Pipeline XML structure

2. **Core Streams** (lines 4746-4800):
   - What core streams are available (PRJM, PRJY, STOM, STOY, IMP, etc.)?
   - How is the CoreStream specified in XML?
   - What's the relationship between CoreStream and Pipeline stages?

3. **Time Range and Filters** (lines 4803-4807):
   - How are time ranges specified for PRJ and STO results?
   - What filter types are available?
   - How do stage filters combine with load filters?

4. **Stream Variables** (lines 4854-4890):
   - How are StreamVariables defined?
   - What's the relationship between Variable and SourceVariable?
   - How do aggregate functions work with stream variables?

Provide exact XML examples and line references from the text file.
```

### Prompt 2.2: Stream Mode Assignments and Expressions
```
From the IDR User Guide text file, analyze Stream mode assignment and expression capabilities:

1. **Basic Assignments** (around lines 4950-4970):
   - How are assignments structured in Stream mode?
   - What source types are available (key, stream, expression)?
   - Extract assignment XML syntax examples

2. **Expression Assignments** (lines 5017-5030):
   - How do expression assignments work with Fragments and Variables?
   - What's the syntax for CDATA sections in expressions?
   - How are custom functions defined in Fragments?

3. **Auto Assign Feature** (lines 4911-4920):
   - What is the autoassign property and how does it work?
   - When are basic assignments created automatically?
   - How to disable autoassign when needed?

4. **Loops in Stream Mode** (lines 5069-5130):
   - What loop types are available (values, for)?
   - How are LoopVariables and LoopValues structured?
   - How do nested loops work?

Extract specific XML examples and line numbers for each feature.
```

---

## Section 3: Advanced Features Prompts

### Prompt 3.1: Stream Mode Merge and Dynamic Dimensions
```
Using the IDR User Guide text file, extract information about advanced Stream mode features:

1. **Merge Functionality** (lines 4399-4425):
   - How does the merge operation work in Stream mode?
   - What attributes enable merge (merge="true", match="true")?
   - How does merge work with IDR vs Snowflake targets?

2. **Dynamic Dimensions** (lines 4414-4416):
   - What is dynamic dimension functionality?
   - How does merge extend dynamic dimension capabilities?
   - What's the difference between single Key column vs multiple match columns?

3. **Snowflake-Specific Features** (lines 4417-4424):
   - How does merge work with Snowflake targets?
   - What is the "use.newmerge" parameter?
   - How are duplicate entries handled?

4. **Temporary Tables** (lines 4431-4452):
   - What are temporary tables and how do they work?
   - How do they behave as both Facts and Dimensions?
   - Extract the TemporaryTable XML structure

Provide exact quotes and line references for each feature.
```

### Prompt 3.2: Stream Mode Validation and Predefined Tables
```
From the IDR User Guide text file, analyze Stream mode validation capabilities:

1. **Predefined Temporary Tables** (lines 4453-4590):
   - What predefined temporary tables are available?
   - Extract the complete table structure for ~~Count~Detail~~, ~~Output~~, ~~Validation~~
   - What are the Key structures and Usable Columns for each?

2. **Validation Pipeline** (lines 4599-4650):
   - How does the validation temporary table work?
   - What is the validate="true" attribute on Pipelines?
   - How are validation levels and exit conditions handled?

3. **Validation Assignment Examples** (lines 4607-4650):
   - Extract the complete validation assignment example
   - How are Key, Level, Message assignments structured?
   - How do filters work with validation targets?

4. **Core Stream for Temporary Tables** (lines 4568-4592):
   - How is the "TEMP-STRING" core stream used?
   - What variables are available for each predefined table?
   - How are temporary tables accessed in pipelines?

Reference specific line numbers and provide complete XML examples.
```

---

## Section 4: Command Line and Configuration Prompts

### Prompt 4.1: Stream Mode Command Line Parameters
```
Using the IDR User Guide text file, extract Stream mode command line information from Chapter 5:

1. **Basic Stream Mode Execution** (around lines 7935-7945):
   - How is the IDR.EXE program executed for Stream mode?
   - What's the basic command syntax: IDR [options] mappingfile?
   - Where is the program located in the installation?

2. **Stream Mode Specific Parameters** (lines 7954-8000):
   - What parameters are specific to Stream mode vs traditional mode?
   - How does compatibility mode work (V1.4.2)?
   - What global value parameters are available?

3. **Connection Parameters** (around lines 7980-8050):
   - How are sourceconnection and targetconnection specified?
   - What connection types are supported in Stream mode?
   - How do vault files work with Stream mode?

4. **Stream Mode vs Traditional Mode** (lines 7972-7974):
   - What's the difference in parameter handling?
   - How does Stream mode evaluate compatibility?
   - What parameters are ignored in Stream mode?

Extract exact parameter syntax and examples from the text file.
```

### Prompt 4.2: Stream Mode Target Connections
```
From the IDR User Guide text file, analyze Stream mode target connections from Chapter 6:

1. **Text Target Connections** (lines 10771-11020):
   - How do Stream mode text targets differ from traditional mode?
   - What file naming patterns are supported ({loadId}, {sequence}, {jobId}, {jobname})?
   - How are control files structured for Stream mode?

2. **AWS S3 Target Connections** (lines 11134-11180):
   - What parameters are required for S3 targets?
   - How are bucket names, regions, and credentials configured?
   - What's the difference between inline parameters and vault files?

3. **Snowflake Target Connections** (lines 11212-11275):
   - What ODBC requirements exist for Snowflake?
   - How are DSN, warehouse, database, and schema configured?
   - What file formats and staging options are available?

4. **Vault File Configuration** (around lines 11180-11210):
   - How are vault files structured for different target types?
   - What sections are required (AWSS3, Text, Snowflake)?
   - Extract complete vault file examples

Provide exact connection string examples and line references.
```

---

## Section 5: Code Generation Prompts

### Prompt 5.1: XML Schema to Stream Mode Mapping
```
Using the IDR User Guide text file, create a mapping strategy from XML Schema to Stream mode:

1. **Dimension Mapping Strategy** (lines 4235-4270):
   - How should XSD complex types map to Stream mode Dimensions?
   - What's the relationship between XSD elements and Key/Id columns?
   - How should XSD attributes become UsableColumns?

2. **Fact Table Generation** (lines 4313-4376):
   - How should XSD elements map to Fact Variables?
   - What data type conversions are needed (XSD types to IDR types)?
   - How should occurrence constraints (minOccurs, maxOccurs) be handled?

3. **Pipeline Generation** (lines 4735-4890):
   - How should XSD structure influence Pipeline design?
   - What StreamVariables should be auto-generated?
   - How should XSD validation rules become Stream mode assignments?

4. **Expression Generation** (lines 5017-5030):
   - How can XSD restrictions become Stream mode expressions?
   - What validation logic should be auto-generated?
   - How should default values be handled in assignments?

Design a complete mapping strategy based on Stream mode capabilities.
```

### Prompt 5.2: Stream Mode Configuration Generator
```
From the IDR User Guide text file, design a configuration generator for Stream mode:

1. **TransformDefinition Generator** (lines 4200-4220):
   - Create a template for generating complete TransformDefinition XML
   - How should Connections be auto-configured?
   - What naming conventions should be used?

2. **Dynamic Pipeline Generation** (lines 4735-4890):
   - How should Pipelines be generated based on data structure?
   - What InitialStage configuration should be default?
   - How should Filters be auto-generated?

3. **Assignment Auto-Generation** (lines 4950-4970):
   - What assignments should be created automatically?
   - How should source="key", source="stream", source="expression" be chosen?
   - When should autoassign="false" be used?

4. **Validation Integration** (lines 4599-4650):
   - How should validation pipelines be auto-generated?
   - What validation rules should be created by default?
   - How should error handling be configured?

Create a complete code generation strategy with XML templates.
```

---

## Section 6: Testing and Validation Prompts

### Prompt 6.1: Stream Mode Testing Framework
```
Using the IDR User Guide text file, design a testing framework for Stream mode:

1. **Validation Testing** (lines 4599-4650):
   - How can the ~~Validation~~ temporary table be used for testing?
   - What test scenarios should validate pipeline processing?
   - How should validation levels be used in testing?

2. **Temporary Table Testing** (lines 4453-4590):
   - How can predefined temporary tables support testing?
   - What metrics should be captured using ~~Count~Detail~~?
   - How should ~~Output~~ table be used for result validation?

3. **Pipeline Testing Strategy** (lines 4735-4890):
   - How should individual pipeline stages be tested?
   - What test data patterns work best with Stream mode?
   - How should CoreStream variations be tested?

4. **Expression Testing** (lines 5017-5030):
   - How should expression assignments be unit tested?
   - What test cases should cover Fragment functionality?
   - How should loop constructs be validated?

Design comprehensive testing approaches based on Stream mode features.
```

### Prompt 6.2: Performance Testing for Stream Mode
```
From the IDR User Guide text file, create performance testing strategies:

1. **Stream Processing Performance** (lines 4854-4890):
   - How should StreamVariable performance be measured?
   - What metrics indicate efficient stream processing?
   - How should aggregate functions be performance tested?

2. **Memory Management Testing** (lines 4431-4452):
   - How should temporary table memory usage be monitored?
   - What are the performance implications of in-memory tables?
   - How should large dataset processing be tested?

3. **Target Connection Performance** (lines 10771-11275):
   - How should different target types (Text, S3, Snowflake) be benchmarked?
   - What performance differences exist between target types?
   - How should file size and batch processing be optimized?

4. **Pipeline Efficiency** (lines 4735-4890):
   - How should multi-stage pipeline performance be measured?
   - What are the performance trade-offs of different CoreStreams?
   - How should filter efficiency be tested?

Create performance testing methodologies specific to Stream mode capabilities.
```

---

## Usage Instructions

### Working with the Text File
1. **Line References**: All prompts include specific line numbers for easy navigation
2. **Search Strategy**: Use text search to find specific sections quickly
3. **Context Extraction**: Extract surrounding context for complete understanding
4. **Cross-References**: Link related sections across different chapters

### Prompt Execution Strategy
1. **Sequential Processing**: Use prompts in order for building understanding
2. **Focused Extraction**: Target specific Stream mode features
3. **Implementation Planning**: Build from concepts to concrete implementation
4. **Validation Integration**: Include testing throughout development

### Expected Outcomes
After using these prompts with the text file, you should have:
- Complete understanding of Stream mode architecture
- Detailed XML mapping strategies
- Implementation-ready code templates
- Comprehensive testing frameworks
- Production deployment guidelines

All based on the exact specifications from IDR User Guide 2024Q4 SP1.
