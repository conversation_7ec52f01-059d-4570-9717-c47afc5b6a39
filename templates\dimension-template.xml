<?xml version="1.0" encoding="utf-8"?>
<!-- IDR Stream Mode Dimension Template -->
<!-- Based on IDR User Guide 2024Q4 SP1, lines 4246-4258 -->

<Dimensions>
  <Dimension autoload="{AUTOLOAD}" dynamic="{DYNAMIC}">
    <Name>{DIMENSION_NAME}</Name>
    <TableName>{TABLE_NAME}</TableName>
    <Connection>{CONNECTION}</Connection>
    <Key type="{KEY_TYPE}">{KEY_COLUMN}</Key>
    <Id>{ID_COLUMN}</Id>
    <UsableColumns>
      <!-- Repeat for each usable column -->
      <UsableColumn type="{USABLE_COLUMN_TYPE}">{USABLE_COLUMN_NAME}</UsableColumn>
    </UsableColumns>
  </Dimension>
</Dimensions>

<!-- 
Template Variables:
- {AUTOLOAD}: "true" or "false" (default: "false")
- {DYNAMIC}: "true" or "false" (default: "false") 
- {DIMENSION_NAME}: Name used in assignments (e.g., "MyProductDimension")
- {TABLE_NAME}: Database table name (e.g., "DimProduct")
- {CONNECTION}: Connection name (e.g., "T1")
- {KEY_TYPE}: Data type for key column (e.g., "string")
- {KEY_COLUMN}: Key column name (e.g., "ProductName")
- {ID_COLUMN}: ID column name (e.g., "ProductId")
- {USABLE_COLUMN_TYPE}: Type of usable column (e.g., "string", "double")
- {USABLE_COLUMN_NAME}: Name of usable column (e.g., "Region", "Factor")

Usage Notes:
- autoload="true" reads all dimension rows during first lookup (more efficient for small dimensions)
- dynamic="true" allows IDR to add missing rows automatically
- Key column must be unique within dimension table
- ID column should be integer (surrogate key)
- UsableColumns can be referenced in assignments
-->
