#!/usr/bin/env python3
"""
Example script demonstrating IDR XML Mapping Generator usage
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from main import IDRXMLMappingGenerator
from core.idr_types import GenerationConfig, CoreStreamType


def run_basic_example():
    """Run basic example with default configuration"""
    print("=" * 60)
    print("Basic Example: Default Configuration")
    print("=" * 60)
    
    # Use sample XSD
    xsd_file = Path(__file__).parent / "sample_input.xsd"
    
    # Create generator with default config
    generator = IDRXMLMappingGenerator()
    
    # Generate IDR XML
    xml_output = generator.generate_from_xsd(str(xsd_file), "ProphetResultsTransform")
    
    # Save output
    output_file = Path(__file__).parent / "output_basic.xml"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(xml_output)
    
    print(f"Generated IDR XML: {output_file}")
    print(f"XML length: {len(xml_output)} characters")
    
    # Validate
    issues = generator.validate_generated_xml(xml_output)
    if issues:
        print(f"Validation issues: {len(issues)}")
        for issue in issues[:5]:  # Show first 5 issues
            print(f"  - {issue}")
    else:
        print("✓ XML validation passed")
    
    print()


def run_advanced_example():
    """Run advanced example with custom configuration"""
    print("=" * 60)
    print("Advanced Example: Custom Configuration")
    print("=" * 60)
    
    # Use sample XSD
    xsd_file = Path(__file__).parent / "sample_input.xsd"
    
    # Create custom configuration
    config = GenerationConfig(
        auto_create_dimensions=True,
        auto_create_facts=True,
        enable_merge_operations=True,
        default_core_stream=CoreStreamType.PRJM,  # Monthly projections
        enable_validation=True,
        naming_convention="PascalCase",
        include_documentation=True
    )
    
    # Create generator
    generator = IDRXMLMappingGenerator(config)
    
    # Generate IDR XML
    xml_output = generator.generate_from_xsd(str(xsd_file), "AdvancedProphetTransform")
    
    # Save output
    output_file = Path(__file__).parent / "output_advanced.xml"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(xml_output)
    
    print(f"Generated IDR XML: {output_file}")
    print(f"XML length: {len(xml_output)} characters")
    
    # Check for advanced features
    if 'merge="true"' in xml_output:
        print("✓ Merge operations enabled")
    if 'validate="true"' in xml_output:
        print("✓ Validation pipeline included")
    if 'PRJM' in xml_output:
        print("✓ Monthly projections core stream")
    
    print()


def run_components_example():
    """Run example generating components separately"""
    print("=" * 60)
    print("Components Example: Separate Generation")
    print("=" * 60)
    
    # Use sample XSD
    xsd_file = Path(__file__).parent / "sample_input.xsd"
    
    # Create generator
    generator = IDRXMLMappingGenerator()
    
    # Generate components separately
    components = generator.generate_components_separately(str(xsd_file))
    
    # Save each component
    for component_name, xml_content in components.items():
        output_file = Path(__file__).parent / f"output_{component_name}.xml"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(xml_content)
        
        print(f"Generated {component_name}: {output_file}")
        print(f"  Length: {len(xml_content)} characters")
    
    print()


def run_different_core_streams():
    """Run examples with different core stream types"""
    print("=" * 60)
    print("Core Streams Example: Different Stream Types")
    print("=" * 60)
    
    xsd_file = Path(__file__).parent / "sample_input.xsd"
    
    core_streams = [
        (CoreStreamType.IMP, "Individual Model Points"),
        (CoreStreamType.PRJM, "Monthly Projections"),
        (CoreStreamType.STOM, "Monthly Stochastic"),
        (CoreStreamType.PRJY, "Yearly Projections")
    ]
    
    for core_stream, description in core_streams:
        print(f"Generating with {description} ({core_stream.value})...")
        
        config = GenerationConfig(default_core_stream=core_stream)
        generator = IDRXMLMappingGenerator(config)
        
        xml_output = generator.generate_from_xsd(
            str(xsd_file), 
            f"Transform_{core_stream.value}"
        )
        
        output_file = Path(__file__).parent / f"output_{core_stream.value.lower()}.xml"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(xml_output)
        
        print(f"  Generated: {output_file}")
        
        # Check for core stream in output
        if core_stream.value in xml_output:
            print(f"  ✓ Contains {core_stream.value} core stream")
        
        # Check for time range (required for projection streams)
        if core_stream in [CoreStreamType.PRJM, CoreStreamType.PRJY, 
                          CoreStreamType.STOM, CoreStreamType.STOY]:
            if "<TimeRange>" in xml_output:
                print(f"  ✓ Contains TimeRange for projection stream")
    
    print()


def analyze_generated_xml():
    """Analyze the generated XML files"""
    print("=" * 60)
    print("Analysis: Generated XML Structure")
    print("=" * 60)
    
    output_files = list(Path(__file__).parent.glob("output_*.xml"))
    
    for output_file in output_files:
        print(f"\nAnalyzing: {output_file.name}")
        
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Count major sections
            sections = {
                'Dimensions': content.count('<Dimension'),
                'Facts': content.count('<Fact'),
                'Pipelines': content.count('<Pipeline'),
                'Variables': content.count('<Variable'),
                'Assignments': content.count('<Assignment'),
                'StreamVariables': content.count('<StreamVariable')
            }
            
            for section, count in sections.items():
                if count > 0:
                    print(f"  {section}: {count}")
            
            # Check for specific features
            features = []
            if 'autoload="true"' in content:
                features.append("Autoload dimensions")
            if 'merge="true"' in content:
                features.append("Merge operations")
            if 'validate="true"' in content:
                features.append("Validation pipeline")
            if 'dynamic="true"' in content:
                features.append("Dynamic dimensions")
            
            if features:
                print(f"  Features: {', '.join(features)}")
            
        except Exception as e:
            print(f"  Error analyzing file: {e}")


def main():
    """Run all examples"""
    print("IDR XML Mapping Generator - Examples")
    print("Based on IDR User Guide 2024Q4 SP1 Stream Mode")
    print()
    
    # Check if sample XSD exists
    xsd_file = Path(__file__).parent / "sample_input.xsd"
    if not xsd_file.exists():
        print(f"Error: Sample XSD file not found: {xsd_file}")
        print("Please ensure sample_input.xsd exists in the examples directory.")
        return
    
    try:
        # Run examples
        run_basic_example()
        run_advanced_example()
        run_components_example()
        run_different_core_streams()
        analyze_generated_xml()
        
        print("=" * 60)
        print("All examples completed successfully!")
        print("=" * 60)
        print()
        print("Generated files:")
        output_files = list(Path(__file__).parent.glob("output_*.xml"))
        for output_file in sorted(output_files):
            print(f"  - {output_file.name}")
        
        print()
        print("Next steps:")
        print("1. Review the generated XML files")
        print("2. Validate against IDR User Guide specifications")
        print("3. Test with IDR command line tools")
        print("4. Customize configuration for your specific needs")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
