# XML Extraction and Analysis Prompts
## Working with IDR Stream Mode XML Examples from the Text File

### Overview
These prompts are designed to extract, analyze, and build upon the actual XML examples found in the IDR User Guide 2024Q4 SP1 text file. The text contains numerous complete XML examples that can serve as templates for your XML mapping generator.

---

## Section 1: Core XML Structure Extraction

### Prompt 1.1: Extract Complete TransformDefinition Template
```
From the IDR User Guide text file (Documentation/IDR_User_Guide_2024Q4_SP1.txt), extract the complete XML structure for Stream mode TransformDefinition:

1. **Root Structure** (around lines 4200-4220):
   - Find the <TransformDefinition> root tag definition
   - Extract the complete section listing (Connections, Dimensions, Facts, etc.)
   - Create a complete XML template with all main sections

2. **Connections Example** (lines 4224-4233):
   - Extract the exact XML for SourceConnections and TargetConnections
   - Note the Connection tag structure
   - Identify naming patterns (S1, S2, T1, T2)

3. **Complete Example Search**:
   - Look for complete TransformDefinition examples throughout Chapter 4
   - Find the most comprehensive example (likely around lines 16160-16300)
   - Extract the full XML structure with all sections populated

Create a master XML template that includes:
- All required sections with proper nesting
- Comment annotations explaining each section
- Placeholder values that can be programmatically replaced
```

### Prompt 1.2: Extract Dimension XML Patterns
```
Using the IDR User Guide text file, extract all Dimension XML examples and create comprehensive patterns:

1. **Basic Dimension Structure** (lines 4246-4270):
   - Extract the complete Dimension XML with all attributes
   - Identify autoload and dynamic attribute usage
   - Note the Name, TableName, Connection, Key, Id structure

2. **UsableColumns Patterns** (around lines 4253-4260):
   - Extract UsableColumn examples with different data types
   - Identify type attribute patterns (string, int, double, etc.)
   - Note how multiple UsableColumns are structured

3. **Advanced Dimension Features**:
   - Search for dynamic="true" examples
   - Find autoload="false" usage patterns
   - Look for complex Key type definitions

4. **Multiple Dimension Examples**:
   - Find examples with multiple Dimension definitions
   - Extract naming conventions and patterns
   - Identify relationship patterns between dimensions

Create dimension templates for:
- Simple lookup dimensions
- Dynamic dimensions with auto-creation
- Dimensions with multiple usable columns
- Complex business hierarchy dimensions
```

---

## Section 2: Facts and Variables Extraction

### Prompt 2.1: Extract Fact Table XML Structures
```
From the IDR User Guide text file, extract comprehensive Fact table examples:

1. **Basic Fact Structure** (lines 4317-4340):
   - Extract the complete Fact XML with Name, TableName, Connection
   - Identify the Variables section structure
   - Note data type patterns (int, double, string, etc.)

2. **Merge-Enabled Facts** (lines 4399-4410):
   - Find Fact examples with merge="true" attribute
   - Extract Variable examples with match="true" attribute
   - Understand the merge operation XML structure

3. **Complex Variable Patterns**:
   - Search for Variables with different type attributes
   - Find examples of Variables with special attributes
   - Look for array variable definitions (if any)

4. **Group TableName Usage** (lines 4378-4398):
   - Extract examples using TableName with "Group:" prefix
   - Understand the group-based table naming pattern
   - Find complete examples of this usage

Create Fact templates for:
- Standard fact tables with basic variables
- Merge-enabled fact tables for upsert operations
- Group-based fact tables
- Complex fact tables with multiple data types
```

### Prompt 2.2: Extract Pipeline and Stage XML Examples
```
Using the IDR User Guide text file, extract comprehensive Pipeline examples:

1. **Basic Pipeline Structure** (lines 4739-4750):
   - Extract the complete Pipeline XML wrapper
   - Find InitialStage examples with CoreStream
   - Identify the basic stage structure

2. **CoreStream Variations** (around lines 4746-4800):
   - Find examples of different CoreStream types (PRJM, PRJY, STOM, etc.)
   - Extract TimeRange specifications for different core streams
   - Identify Filters usage with different core streams

3. **Multi-Stage Pipeline Examples**:
   - Search for Pipeline examples with multiple stages
   - Find Stage definitions with KeyProperties
   - Extract custom stream examples

4. **Complete Pipeline Examples** (around lines 16160-16300):
   - Find the most complete Pipeline example in the user interface chapter
   - Extract the full XML with all components
   - Identify all sub-elements (StreamVariables, Assignments, Targets, etc.)

Create Pipeline templates for:
- Single-stage pipelines with different core streams
- Multi-stage pipelines with grouping
- Pipelines with complex filtering
- Validation-enabled pipelines
```

---

## Section 3: StreamVariables and Assignments Extraction

### Prompt 3.1: Extract StreamVariable Patterns
```
From the IDR User Guide text file, extract all StreamVariable examples and patterns:

1. **Basic StreamVariable Structure** (lines 4857-4870):
   - Extract the Variable and SourceVariable pairing
   - Identify type attribute usage (double, int, string)
   - Note the autoassign attribute behavior

2. **Aggregate Function Examples** (lines 4889-4890):
   - Find StreamVariable examples with Aggregate tags
   - Extract different aggregate functions (sum, max, count, etc.)
   - Understand when aggregates are required

3. **Array Variable Examples** (lines 4892-4910):
   - Extract the array variable syntax with index1 attribute
   - Understand the equivalent individual variable expansion
   - Find complete array variable examples

4. **Auto Assign Examples** (lines 4912-4920):
   - Find examples with autoassign="true" and autoassign="false"
   - Understand when basic assignments are auto-created
   - Extract the default behavior patterns

Create StreamVariable templates for:
- Basic variable mappings with auto-assignment
- Aggregate variables for grouping operations
- Array variables for Prophet array handling
- Custom variables with specific assignment control
```

### Prompt 3.2: Extract Assignment XML Patterns
```
Using the IDR User Guide text file, extract comprehensive Assignment examples:

1. **Basic Assignment Types** (lines 4956-4970):
   - Extract assignments with source="key"
   - Find assignments with source="stream"
   - Identify assignments with source="expression"

2. **Expression Assignments** (lines 5020-5040):
   - Extract the complete expression assignment structure
   - Find Fragment examples with CDATA sections
   - Identify Variables usage within expressions

3. **Complex Expression Examples** (lines 5703-5720):
   - Find the complete expression assignment example with functions
   - Extract Fragment definitions with custom functions
   - Understand the symbol attribute usage

4. **Filter Integration** (around lines 5203-5215):
   - Find Assignment examples with Filter elements
   - Extract filter expressions and their syntax
   - Understand conditional assignment patterns

Create Assignment templates for:
- Direct value assignments from keys and streams
- Complex expression assignments with custom functions
- Conditional assignments with filters
- Multi-fragment expression assignments
```

---

## Section 4: Advanced Features Extraction

### Prompt 4.1: Extract Loop and Control Structure Examples
```
From the IDR User Guide text file, extract Loop and control structure examples:

1. **Values Loop Examples** (lines 5070-5090):
   - Extract the complete Loop with looptype="values"
   - Find LoopVariables and LoopValues structure
   - Understand the iteration pattern

2. **For Loop Examples** (lines 5100-5120):
   - Find Loop examples with looptype="for"
   - Extract the range-based loop structure
   - Understand numeric iteration patterns

3. **Nested Loop Examples** (lines 5126-5140):
   - Find examples of nested loop structures
   - Extract the parent-child loop relationships
   - Understand complex iteration patterns

4. **Loop with Assignments** (lines 5234-5245):
   - Find complete examples combining loops with assignments
   - Extract the output pattern for loop iterations
   - Understand variable substitution in loops

Create Loop templates for:
- Simple value iteration loops
- Numeric range loops
- Nested loop structures
- Loops integrated with assignments and expressions
```

### Prompt 4.2: Extract Validation and Temporary Table Examples
```
Using the IDR User Guide text file, extract validation and temporary table examples:

1. **Temporary Table Definitions** (lines 4441-4451):
   - Extract the complete TemporaryTable XML structure
   - Find Key, Id, and UsableColumns definitions
   - Understand the in-memory table structure

2. **Validation Pipeline Example** (lines 4609-4650):
   - Extract the complete validation pipeline example
   - Find the validate="true" attribute usage
   - Understand the ~~Validation~~ target usage

3. **Predefined Table Usage** (lines 4568-4590):
   - Find examples using predefined temporary tables
   - Extract the TEMP-STRING core stream usage
   - Understand the temporarytable attribute

4. **Validation Assignment Patterns** (lines 4635-4647):
   - Extract the Key, Level, Message assignment pattern
   - Find the Filter usage with validation targets
   - Understand the validation row creation process

Create templates for:
- Custom temporary table definitions
- Validation pipeline configurations
- Predefined temporary table usage
- Error detection and reporting patterns
```

---

## Section 5: Complete Example Extraction

### Prompt 5.1: Extract User Interface Generated XML
```
From the IDR User Guide text file, extract the complete XML example from the user interface chapter:

1. **Complete TransformDefinition** (lines 16162-16300):
   - Extract the full XML example generated by the user interface
   - Identify all sections: Connections, Dimensions, Facts, Pipelines
   - Note the complete structure and nesting

2. **Real-World Patterns**:
   - Analyze the naming conventions used
   - Identify the data types and structures
   - Note the Pipeline configuration with all components

3. **Component Integration**:
   - See how all components work together
   - Understand the relationship between Dimensions and Facts
   - Analyze the Pipeline flow from StreamVariables to Targets

4. **Production-Ready Structure**:
   - Extract the complete, working XML structure
   - Identify best practices in the example
   - Note any advanced features being used

Use this as the master template for:
- Complete XML generation
- Component integration patterns
- Production-ready configurations
- Best practice implementations
```

### Prompt 5.2: Extract Command Line Integration Examples
```
Using the IDR User Guide text file, extract command line usage examples for Stream mode:

1. **Basic Stream Mode Commands** (around lines 11090-11140):
   - Extract complete command line examples
   - Find sourceconnection and targetconnection patterns
   - Identify parameter combinations for different scenarios

2. **Target Connection Examples** (lines 11020-11275):
   - Extract Text target connection examples
   - Find AWS S3 target connection commands
   - Extract Snowflake target connection examples

3. **Vault File Integration** (around lines 11180-11210):
   - Find command examples using vault files
   - Extract vault file structure examples
   - Understand the vaultsectionname parameter usage

4. **Complete Workflow Examples**:
   - Find end-to-end command examples
   - Extract parameter combinations for different use cases
   - Identify best practices for production deployment

Create command templates for:
- Development and testing scenarios
- Production deployment with vault files
- Different target connection types
- Multi-connection scenarios
```

---

## Usage Instructions

### Extraction Strategy
1. **Use Line Numbers**: Navigate directly to specified lines in the text file
2. **Extract Complete Examples**: Get full XML blocks, not just fragments
3. **Preserve Formatting**: Maintain XML structure and indentation
4. **Note Context**: Include surrounding explanatory text

### Template Creation
1. **Parameterize Values**: Replace specific values with placeholders
2. **Add Comments**: Document each section and attribute
3. **Create Variants**: Build templates for different use cases
4. **Validate Structure**: Ensure XML is well-formed

### Integration Planning
1. **Map to Generator**: Plan how each template fits into your generator
2. **Identify Patterns**: Find reusable patterns across examples
3. **Plan Customization**: Identify what needs to be configurable
4. **Design Validation**: Plan how to validate generated XML

### Expected Outcomes
After using these prompts, you should have:
- Complete XML templates for all Stream mode components
- Real-world examples from the user guide
- Command line integration patterns
- Production-ready configuration templates
- Comprehensive understanding of XML structure relationships
