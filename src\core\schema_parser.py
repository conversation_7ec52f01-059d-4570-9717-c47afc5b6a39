"""
XSD Schema Parser for IDR XML Mapping Generator
Based on IDR User Guide 2024Q4 SP1 Stream Mode requirements
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class XSDType(Enum):
    """XSD primitive types"""
    STRING = "string"
    INT = "int"
    INTEGER = "integer"
    DECIMAL = "decimal"
    DOUBLE = "double"
    FLOAT = "float"
    BOOLEAN = "boolean"
    DATE = "date"
    DATETIME = "dateTime"
    TIME = "time"


class IDRType(Enum):
    """IDR Stream mode types from User Guide lines 4323-4335"""
    STRING = "string"
    INT = "int"
    DOUBLE = "double"
    BOOL = "bool"
    LONG = "long"


@dataclass
class XSDElement:
    """Represents an XSD element"""
    name: str
    type: str
    min_occurs: int = 1
    max_occurs: int = 1
    is_attribute: bool = False
    namespace: Optional[str] = None
    documentation: Optional[str] = None
    restrictions: Dict[str, Any] = None

    def __post_init__(self):
        if self.restrictions is None:
            self.restrictions = {}


@dataclass
class XSDComplexType:
    """Represents an XSD complex type"""
    name: str
    elements: List[XSDElement]
    attributes: List[XSDElement]
    namespace: Optional[str] = None
    base_type: Optional[str] = None


@dataclass
class SchemaAnalysis:
    """Complete schema analysis result"""
    target_namespace: Optional[str]
    elements: List[XSDElement]
    complex_types: List[XSDComplexType]
    namespaces: Dict[str, str]
    root_elements: List[XSDElement]


class TypeMapper:
    """
    Maps XSD types to IDR types based on User Guide examples
    From lines 4323-4335: int, double, string types
    """
    
    XSD_TO_IDR_MAPPING = {
        XSDType.STRING: IDRType.STRING,
        XSDType.INT: IDRType.INT,
        XSDType.INTEGER: IDRType.INT,
        XSDType.DECIMAL: IDRType.DOUBLE,
        XSDType.DOUBLE: IDRType.DOUBLE,
        XSDType.FLOAT: IDRType.DOUBLE,
        XSDType.BOOLEAN: IDRType.BOOL,
        XSDType.DATE: IDRType.STRING,  # Dates as strings in IDR
        XSDType.DATETIME: IDRType.STRING,
        XSDType.TIME: IDRType.STRING,
    }

    @classmethod
    def map_xsd_to_idr(cls, xsd_type: str) -> str:
        """Map XSD type to IDR type"""
        # Clean up type name (remove namespace prefixes)
        clean_type = xsd_type.split(':')[-1].lower()
        
        try:
            xsd_enum = XSDType(clean_type)
            idr_enum = cls.XSD_TO_IDR_MAPPING.get(xsd_enum, IDRType.STRING)
            return idr_enum.value
        except ValueError:
            logger.warning(f"Unknown XSD type: {xsd_type}, defaulting to string")
            return IDRType.STRING.value

    @classmethod
    def get_default_value(cls, idr_type: str) -> str:
        """Get default value for IDR type"""
        defaults = {
            IDRType.STRING.value: '""',
            IDRType.INT.value: "0",
            IDRType.DOUBLE.value: "0.0",
            IDRType.BOOL.value: "false",
            IDRType.LONG.value: "0"
        }
        return defaults.get(idr_type, '""')


class XSDParser:
    """
    Parses XSD files and extracts structure for IDR mapping
    Based on Stream mode requirements from User Guide
    """

    def __init__(self):
        self.namespaces = {}
        self.type_mapper = TypeMapper()

    def parse_schema(self, xsd_file_path: str) -> SchemaAnalysis:
        """Parse XSD file and return analysis"""
        try:
            tree = ET.parse(xsd_file_path)
            root = tree.getroot()
            
            # Extract namespaces
            self.namespaces = self._extract_namespaces(root)
            
            # Get target namespace
            target_namespace = root.get('targetNamespace')
            
            # Parse elements and complex types
            elements = self._parse_elements(root)
            complex_types = self._parse_complex_types(root)
            root_elements = self._find_root_elements(elements, complex_types)
            
            return SchemaAnalysis(
                target_namespace=target_namespace,
                elements=elements,
                complex_types=complex_types,
                namespaces=self.namespaces,
                root_elements=root_elements
            )
            
        except Exception as e:
            logger.error(f"Error parsing XSD file {xsd_file_path}: {e}")
            raise

    def _extract_namespaces(self, root: ET.Element) -> Dict[str, str]:
        """Extract namespace declarations"""
        namespaces = {}
        for key, value in root.attrib.items():
            if key.startswith('xmlns'):
                prefix = key.split(':')[1] if ':' in key else 'default'
                namespaces[prefix] = value
        return namespaces

    def _parse_elements(self, root: ET.Element) -> List[XSDElement]:
        """Parse all elements in the schema"""
        elements = []
        
        # Find all element definitions
        for elem in root.iter():
            if elem.tag.endswith('}element') or elem.tag == 'element':
                element = self._parse_element(elem)
                if element:
                    elements.append(element)
        
        return elements

    def _parse_element(self, elem: ET.Element) -> Optional[XSDElement]:
        """Parse a single element"""
        name = elem.get('name')
        if not name:
            return None

        element_type = elem.get('type', 'string')
        min_occurs = int(elem.get('minOccurs', '1'))
        max_occurs_str = elem.get('maxOccurs', '1')
        max_occurs = float('inf') if max_occurs_str == 'unbounded' else int(max_occurs_str)
        
        # Extract documentation
        documentation = self._extract_documentation(elem)
        
        # Extract restrictions
        restrictions = self._extract_restrictions(elem)
        
        return XSDElement(
            name=name,
            type=element_type,
            min_occurs=min_occurs,
            max_occurs=max_occurs,
            documentation=documentation,
            restrictions=restrictions
        )

    def _parse_complex_types(self, root: ET.Element) -> List[XSDComplexType]:
        """Parse complex type definitions"""
        complex_types = []
        
        for elem in root.iter():
            if elem.tag.endswith('}complexType') or elem.tag == 'complexType':
                complex_type = self._parse_complex_type(elem)
                if complex_type:
                    complex_types.append(complex_type)
        
        return complex_types

    def _parse_complex_type(self, elem: ET.Element) -> Optional[XSDComplexType]:
        """Parse a single complex type"""
        name = elem.get('name')
        if not name:
            return None

        elements = []
        attributes = []
        
        # Parse child elements
        for child in elem.iter():
            if child.tag.endswith('}element') or child.tag == 'element':
                element = self._parse_element(child)
                if element:
                    elements.append(element)
            elif child.tag.endswith('}attribute') or child.tag == 'attribute':
                attribute = self._parse_attribute(child)
                if attribute:
                    attributes.append(attribute)
        
        return XSDComplexType(
            name=name,
            elements=elements,
            attributes=attributes
        )

    def _parse_attribute(self, elem: ET.Element) -> Optional[XSDElement]:
        """Parse an attribute as an element"""
        name = elem.get('name')
        if not name:
            return None

        attr_type = elem.get('type', 'string')
        use = elem.get('use', 'optional')
        min_occurs = 0 if use == 'optional' else 1
        
        return XSDElement(
            name=name,
            type=attr_type,
            min_occurs=min_occurs,
            max_occurs=1,
            is_attribute=True
        )

    def _extract_documentation(self, elem: ET.Element) -> Optional[str]:
        """Extract documentation from element"""
        for child in elem:
            if child.tag.endswith('}annotation') or child.tag == 'annotation':
                for doc in child:
                    if doc.tag.endswith('}documentation') or doc.tag == 'documentation':
                        return doc.text
        return None

    def _extract_restrictions(self, elem: ET.Element) -> Dict[str, Any]:
        """Extract restrictions from element"""
        restrictions = {}
        
        for child in elem.iter():
            if child.tag.endswith('}restriction') or child.tag == 'restriction':
                for restriction in child:
                    tag = restriction.tag.split('}')[-1] if '}' in restriction.tag else restriction.tag
                    value = restriction.get('value')
                    if value:
                        restrictions[tag] = value
        
        return restrictions

    def _find_root_elements(self, elements: List[XSDElement], 
                          complex_types: List[XSDComplexType]) -> List[XSDElement]:
        """Find root elements that could be entry points"""
        # Simple heuristic: elements not referenced by complex types
        referenced_elements = set()
        
        for complex_type in complex_types:
            for element in complex_type.elements:
                referenced_elements.add(element.name)
        
        root_elements = [elem for elem in elements if elem.name not in referenced_elements]
        
        # If no clear root elements, return first few elements
        if not root_elements and elements:
            root_elements = elements[:3]
        
        return root_elements


class ConstraintAnalyzer:
    """
    Analyzes XSD constraints and converts them to IDR validation rules
    Based on validation patterns from User Guide lines 4599-4650
    """

    def __init__(self):
        self.validation_rules = []

    def analyze_constraints(self, schema_analysis: SchemaAnalysis) -> List[Dict[str, Any]]:
        """Analyze schema constraints and generate validation rules"""
        validation_rules = []

        for element in schema_analysis.elements:
            rules = self._analyze_element_constraints(element)
            validation_rules.extend(rules)

        for complex_type in schema_analysis.complex_types:
            rules = self._analyze_complex_type_constraints(complex_type)
            validation_rules.extend(rules)

        return validation_rules

    def _analyze_element_constraints(self, element: XSDElement) -> List[Dict[str, Any]]:
        """Analyze constraints for a single element"""
        rules = []

        # Required field validation
        if element.min_occurs > 0:
            rules.append({
                'type': 'required',
                'field': element.name,
                'message': f'{element.name} is required',
                'expression': f'{element.name} != null && {element.name} != ""'
            })

        # Length restrictions
        if 'minLength' in element.restrictions:
            min_length = element.restrictions['minLength']
            rules.append({
                'type': 'minLength',
                'field': element.name,
                'value': min_length,
                'message': f'{element.name} must be at least {min_length} characters',
                'expression': f'length({element.name}) >= {min_length}'
            })

        if 'maxLength' in element.restrictions:
            max_length = element.restrictions['maxLength']
            rules.append({
                'type': 'maxLength',
                'field': element.name,
                'value': max_length,
                'message': f'{element.name} must be at most {max_length} characters',
                'expression': f'length({element.name}) <= {max_length}'
            })

        # Pattern restrictions
        if 'pattern' in element.restrictions:
            pattern = element.restrictions['pattern']
            rules.append({
                'type': 'pattern',
                'field': element.name,
                'value': pattern,
                'message': f'{element.name} must match pattern {pattern}',
                'expression': f'matches({element.name}, "{pattern}")'
            })

        # Numeric range restrictions
        if 'minInclusive' in element.restrictions:
            min_val = element.restrictions['minInclusive']
            rules.append({
                'type': 'minValue',
                'field': element.name,
                'value': min_val,
                'message': f'{element.name} must be >= {min_val}',
                'expression': f'{element.name} >= {min_val}'
            })

        if 'maxInclusive' in element.restrictions:
            max_val = element.restrictions['maxInclusive']
            rules.append({
                'type': 'maxValue',
                'field': element.name,
                'value': max_val,
                'message': f'{element.name} must be <= {max_val}',
                'expression': f'{element.name} <= {max_val}'
            })

        return rules

    def _analyze_complex_type_constraints(self, complex_type: XSDComplexType) -> List[Dict[str, Any]]:
        """Analyze constraints for complex types"""
        rules = []

        # Analyze all elements in the complex type
        for element in complex_type.elements:
            element_rules = self._analyze_element_constraints(element)
            rules.extend(element_rules)

        # Analyze attributes
        for attribute in complex_type.attributes:
            attr_rules = self._analyze_element_constraints(attribute)
            rules.extend(attr_rules)

        return rules
