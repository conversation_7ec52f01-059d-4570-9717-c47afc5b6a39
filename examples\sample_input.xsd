<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://prophet.example.com/results"
           xmlns:tns="http://prophet.example.com/results"
           elementFormDefault="qualified">

  <!-- Prophet Results Schema for IDR XML Mapping Generator -->
  
  <!-- Root element for Prophet results -->
  <xs:element name="ProphetResults" type="tns:ProphetResultsType"/>
  
  <!-- Main results structure -->
  <xs:complexType name="ProphetResultsType">
    <xs:sequence>
      <xs:element name="RunInfo" type="tns:RunInfoType"/>
      <xs:element name="ModelPoints" type="tns:ModelPointsType"/>
      <xs:element name="Results" type="tns:ResultsType"/>
    </xs:sequence>
    <xs:attribute name="version" type="xs:string" use="required"/>
  </xs:complexType>
  
  <!-- Run information -->
  <xs:complexType name="RunInfoType">
    <xs:sequence>
      <xs:element name="RunNumber" type="xs:int"/>
      <xs:element name="RunDate" type="xs:date"/>
      <xs:element name="RunDescription" type="xs:string"/>
      <xs:element name="SPCode" type="xs:int"/>
      <xs:element name="ProductCode" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  
  <!-- Model points structure -->
  <xs:complexType name="ModelPointsType">
    <xs:sequence>
      <xs:element name="ModelPoint" type="tns:ModelPointType" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  
  <!-- Individual model point -->
  <xs:complexType name="ModelPointType">
    <xs:sequence>
      <xs:element name="PolicyNumber" type="xs:string"/>
      <xs:element name="ProductId" type="xs:int"/>
      <xs:element name="IssueAge" type="xs:int"/>
      <xs:element name="Gender" type="tns:GenderType"/>
      <xs:element name="SumAssured" type="xs:decimal"/>
      <xs:element name="PremiumFrequency" type="tns:FrequencyType"/>
      <xs:element name="PolicyStatus" type="tns:StatusType"/>
    </xs:sequence>
    <xs:attribute name="modelPointId" type="xs:int" use="required"/>
  </xs:complexType>
  
  <!-- Results structure -->
  <xs:complexType name="ResultsType">
    <xs:sequence>
      <xs:element name="TimeSeriesResults" type="tns:TimeSeriesResultsType"/>
      <xs:element name="SummaryResults" type="tns:SummaryResultsType"/>
    </xs:sequence>
  </xs:complexType>
  
  <!-- Time series results -->
  <xs:complexType name="TimeSeriesResultsType">
    <xs:sequence>
      <xs:element name="TimePoint" type="tns:TimePointType" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  
  <!-- Individual time point results -->
  <xs:complexType name="TimePointType">
    <xs:sequence>
      <xs:element name="MonthId" type="xs:int"/>
      <xs:element name="DEATH_OUTGO" type="xs:decimal"/>
      <xs:element name="SURR_OUTGO" type="xs:decimal"/>
      <xs:element name="ANNUAL_PREM" type="xs:decimal"/>
      <xs:element name="RESERVES" type="xs:decimal"/>
      <xs:element name="CASH_FLOW" type="xs:decimal"/>
      <xs:element name="PRESENT_VALUE" type="xs:decimal"/>
      <xs:element name="NEW_BUSINESS" type="xs:decimal"/>
      <xs:element name="POLICY_COUNT" type="xs:int"/>
      <xs:element name="LAPSE_RATE" type="xs:decimal"/>
      <xs:element name="MORTALITY_RATE" type="xs:decimal"/>
    </xs:sequence>
    <xs:attribute name="timeId" type="xs:int" use="required"/>
  </xs:complexType>
  
  <!-- Summary results -->
  <xs:complexType name="SummaryResultsType">
    <xs:sequence>
      <xs:element name="TotalPremium" type="xs:decimal"/>
      <xs:element name="TotalBenefits" type="xs:decimal"/>
      <xs:element name="NetPresentValue" type="xs:decimal"/>
      <xs:element name="InternalRateOfReturn" type="xs:decimal"/>
      <xs:element name="Profitability" type="xs:decimal"/>
    </xs:sequence>
  </xs:complexType>
  
  <!-- Enumeration types -->
  <xs:simpleType name="GenderType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="M"/>
      <xs:enumeration value="F"/>
    </xs:restriction>
  </xs:simpleType>
  
  <xs:simpleType name="FrequencyType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Annual"/>
      <xs:enumeration value="SemiAnnual"/>
      <xs:enumeration value="Quarterly"/>
      <xs:enumeration value="Monthly"/>
    </xs:restriction>
  </xs:simpleType>
  
  <xs:simpleType name="StatusType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Active"/>
      <xs:enumeration value="Lapsed"/>
      <xs:enumeration value="Surrendered"/>
      <xs:enumeration value="Matured"/>
      <xs:enumeration value="Death"/>
    </xs:restriction>
  </xs:simpleType>
  
  <!-- Standalone lookup elements -->
  <xs:element name="ProductCode" type="xs:string"/>
  <xs:element name="RegionCode" type="xs:string"/>
  <xs:element name="AgentCode" type="xs:string"/>
  <xs:element name="ChannelCode" type="xs:string"/>
  
  <!-- Constrained elements for validation -->
  <xs:element name="PolicyNumber">
    <xs:simpleType>
      <xs:restriction base="xs:string">
        <xs:minLength value="8"/>
        <xs:maxLength value="15"/>
        <xs:pattern value="[A-Z]{2}[0-9]{6,13}"/>
      </xs:restriction>
    </xs:simpleType>
  </xs:element>
  
  <xs:element name="PremiumAmount">
    <xs:simpleType>
      <xs:restriction base="xs:decimal">
        <xs:minInclusive value="0.01"/>
        <xs:maxInclusive value="999999.99"/>
        <xs:fractionDigits value="2"/>
      </xs:restriction>
    </xs:simpleType>
  </xs:element>
  
</xs:schema>
